'use client';

interface SpendManagementProps {
  isSelected?: boolean;
}

export function SpendManagement({ isSelected }: SpendManagementProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 23 20"
      width={23}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.132 7.708H2.614c-.367 0-.672-.283-.672-.625s.305-.625.672-.625h8.518c.367 0 .672.283.672.625s-.305.625-.672.625Zm-3.138 6.667H6.2c-.368 0-.673-.283-.673-.625s.305-.625.673-.625h1.793c.367 0 .672.283.672.625s-.305.625-.672.625Zm5.828 0h-3.587c-.367 0-.672-.283-.672-.625s.305-.625.672-.625h3.587c.367 0 .672.283.672.625s-.305.625-.672.625Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M16.565 17.709h-9.97c-3.568 0-4.653-1-4.653-4.284v-6.85c0-3.283 1.085-4.283 4.653-4.283h7.227c.367 0 .672.283.672.625s-.305.625-.672.625H6.595c-2.815 0-3.308.45-3.308 3.033v6.842c0 2.583.493 3.033 3.308 3.033h9.961c2.816 0 3.309-.45 3.309-3.033v-3.4c0-.342.304-.625.672-.625.368 0 .672.283.672.625v3.4c.01 3.292-1.075 4.292-4.644 4.292Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M14.736 9.533c-.35 0-.681-.116-.914-.341a1.162 1.162 0 0 1-.35-1.05l.18-1.184c.044-.3.241-.658.466-.866L17.444 3c1.318-1.225 2.465-.525 3.04 0 .564.525 1.317 1.592 0 2.825l-3.327 3.092a1.865 1.865 0 0 1-.933.433l-1.273.167a1.926 1.926 0 0 1-.215.016Zm4.223-5.966c-.18 0-.34.116-.565.316l-3.317 3.092a.61.61 0 0 0-.09.167l-.18 1.125 1.22-.159a.694.694 0 0 0 .18-.083l3.326-3.092c.421-.391.484-.608 0-1.05-.233-.216-.404-.316-.574-.316Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M19.533 6.45a.618.618 0 0 1-.18-.025C18.126 6.1 17.149 5.2 16.808 4.06c-.098-.334.108-.675.466-.775.36-.092.727.1.834.433.224.725.843 1.3 1.623 1.508.359.092.565.442.466.767a.704.704 0 0 1-.663.458Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
