'use client';

interface PayrollProps {
  isSelected?: boolean;
}

export function Payroll({ isSelected }: PayrollProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.183 9.792h-2.85a.63.63 0 0 1-.625-.625V3.342c0-.617.242-1.192.675-1.625a2.282 2.282 0 0 1 1.625-.675h.009A3.968 3.968 0 0 1 17.792 2.2 3.97 3.97 0 0 1 18.95 5v2.017c.008 1.658-1.108 2.775-2.767 2.775Zm-2.225-1.25h2.225c.967 0 1.525-.558 1.525-1.525V5c0-.716-.283-1.4-.791-1.916a2.745 2.745 0 0 0-1.9-.792h-.009c-.275 0-.541.108-.741.308-.2.2-.309.459-.309.742v5.2Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M7.5 19.442c-.392 0-.759-.15-1.034-.433l-1.383-1.392a.205.205 0 0 0-.275-.017l-1.425 1.067a1.444 1.444 0 0 1-1.525.142c-.5-.25-.808-.75-.808-1.309V5c0-2.516 1.441-3.958 3.958-3.958h10a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625c-.575 0-1.041.467-1.041 1.042V17.5c0 .559-.309 1.059-.809 1.309a1.445 1.445 0 0 1-1.525-.142L10.208 17.6a.203.203 0 0 0-.267.017l-1.4 1.4c-.283.275-.65.425-1.041.425Zm-2.575-3.133c.383 0 .758.141 1.041.433l1.384 1.392c.05.05.116.058.15.058.033 0 .1-.008.15-.058l1.4-1.4a1.45 1.45 0 0 1 1.908-.125l1.417 1.058a.19.19 0 0 0 .216.017c.042-.025.117-.075.117-.184V3.334c0-.375.092-.734.25-1.042H5c-1.85 0-2.71.858-2.71 2.708v12.5c0 .117.075.167.117.192.05.025.133.042.217-.025L4.05 16.6c.258-.191.566-.291.875-.291Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 8.125H5a.63.63 0 0 1-.625-.625A.63.63 0 0 1 5 6.875h5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm-.625 3.333h-3.75A.63.63 0 0 1 5 10.833a.63.63 0 0 1 .625-.625h3.75a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
