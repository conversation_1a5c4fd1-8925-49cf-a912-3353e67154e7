import * as React from "react";
import { SVGProps } from "react";
const FilterIcn = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14.6667 4.33333H10.6667"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.99992 4.33333H1.33325"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.66659 6.66667C7.95525 6.66667 8.99992 5.622 8.99992 4.33333C8.99992 3.04467 7.95525 2 6.66659 2C5.37792 2 4.33325 3.04467 4.33325 4.33333C4.33325 5.622 5.37792 6.66667 6.66659 6.66667Z"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.6667 11.6667H12"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.33325 11.6667H1.33325"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.33333 14C10.622 14 11.6667 12.9553 11.6667 11.6667C11.6667 10.378 10.622 9.33333 9.33333 9.33333C8.04467 9.33333 7 10.378 7 11.6667C7 12.9553 8.04467 14 9.33333 14Z"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default FilterIcn;
