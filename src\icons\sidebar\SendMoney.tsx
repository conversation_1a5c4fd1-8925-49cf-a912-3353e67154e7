'use client';

interface SendMoneyProps {
  isSelected?: boolean;
}

export function SendMoney({ isSelected }: SendMoneyProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.85 18.025c-.983 0-2.375-.691-3.475-4l-.6-1.8-1.8-.6c-3.3-1.1-3.992-2.491-3.992-3.475 0-.975.692-2.375 3.992-3.483L13.05 2.31c1.767-.592 3.242-.417 4.15.483.908.9 1.083 2.383.492 4.15l-2.359 7.075c-1.108 3.317-2.5 4.008-3.483 4.008ZM6.367 5.86c-2.317.774-3.142 1.69-3.142 2.29 0 .6.825 1.517 3.142 2.284l2.1.7a.612.612 0 0 1 .391.391l.7 2.1c.767 2.317 1.692 3.142 2.292 3.142.6 0 1.517-.825 2.292-3.142L16.5 6.55c.425-1.283.35-2.333-.192-2.875-.541-.541-1.591-.608-2.866-.183L6.367 5.86Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M8.425 12a.618.618 0 0 1-.442-.183.629.629 0 0 1 0-.883l2.984-2.992a.629.629 0 0 1 .883 0 .629.629 0 0 1 0 .883l-2.983 2.992a.604.604 0 0 1-.442.183Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
