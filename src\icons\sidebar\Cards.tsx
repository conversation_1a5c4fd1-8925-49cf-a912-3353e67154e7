'use client';

interface CardsProps {
  isSelected?: boolean;
}

export function Cards({ isSelected }: CardsProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.8334 11.1333H1.66669C1.32502 11.1333 1.04169 10.85 1.04169 10.5083C1.04169 10.1666 1.32502 9.8833 1.66669 9.8833H15.8334C16.175 9.8833 16.4584 10.1666 16.4584 10.5083C16.4584 10.85 16.175 11.1333 15.8334 11.1333Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M12.6833 18.125H4.81671C1.95838 18.125 1.04169 17.2167 1.04169 14.3917V8.56671C1.04169 6.32504 1.55004 5.00004 4.13337 4.85004C4.35004 4.84171 4.57504 4.83337 4.81671 4.83337H12.6833C15.5417 4.83337 16.4584 5.74171 16.4584 8.56671V14.525C16.425 17.25 15.5083 18.125 12.6833 18.125ZM4.81671 6.08337C4.59171 6.08337 4.38338 6.09171 4.19171 6.10004C2.70004 6.19171 2.29169 6.50837 2.29169 8.56671V14.3917C2.29169 16.525 2.64171 16.875 4.81671 16.875H12.6833C14.8333 16.875 15.1834 16.5417 15.2084 14.5167V8.56671C15.2084 6.43337 14.8583 6.08337 12.6833 6.08337H4.81671V6.08337Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M15.8334 15.15C15.675 15.15 15.5167 15.0917 15.4083 14.9833C15.2833 14.8667 15.2084 14.7 15.2084 14.525V8.56667C15.2084 6.43333 14.8583 6.08333 12.6833 6.08333H4.81671C4.59171 6.08333 4.38338 6.09167 4.19171 6.1C4.02504 6.10833 3.85834 6.04167 3.73334 5.925C3.60834 5.80833 3.54169 5.64167 3.54169 5.46667C3.57502 2.75 4.49171 1.875 7.31671 1.875H15.1833C18.0417 1.875 18.9584 2.78333 18.9584 5.60833V11.4333C18.9584 13.675 18.45 15 15.8667 15.15C15.8583 15.15 15.8417 15.15 15.8334 15.15ZM4.81671 4.83333H12.6833C15.5417 4.83333 16.4584 5.74167 16.4584 8.56667V13.8333C17.425 13.6583 17.7084 13.1583 17.7084 11.4333V5.60833C17.7084 3.475 17.3583 3.125 15.1833 3.125H7.31671C5.41671 3.125 4.92504 3.38333 4.81671 4.83333Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M5.80024 15.4667H4.36688C4.02522 15.4667 3.74188 15.1833 3.74188 14.8417C3.74188 14.5 4.02522 14.2167 4.36688 14.2167H5.80024C6.14191 14.2167 6.42524 14.5 6.42524 14.8417C6.42524 15.1833 6.15024 15.4667 5.80024 15.4667Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10.4582 15.4667H7.59155C7.24989 15.4667 6.96655 15.1833 6.96655 14.8417C6.96655 14.5 7.24989 14.2167 7.59155 14.2167H10.4582C10.7999 14.2167 11.0832 14.5 11.0832 14.8417C11.0832 15.1833 10.8082 15.4667 10.4582 15.4667Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
