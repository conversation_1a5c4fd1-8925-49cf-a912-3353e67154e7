'use client';

interface StockProps {
  isSelected?: boolean;
}

export function Stock({ isSelected }: StockProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.833 18.958a3.95 3.95 0 0 1-3.341-1.85c-.384-.592-.6-1.3-.617-2.025a3.914 3.914 0 0 1 1.417-3.117 4.016 4.016 0 0 1 2.45-.925c1.083-.008 2.058.367 2.825 1.1a3.897 3.897 0 0 1 1.216 2.775 3.879 3.879 0 0 1-.533 2.058c-.2.35-.458.675-.767.95a3.855 3.855 0 0 1-2.575 1.034h-.075Zm0-6.667h-.058c-.617.017-1.2.233-1.683.633a2.677 2.677 0 0 0-.967 2.134c.008.492.158.975.417 1.383.508.817 1.391 1.317 2.341 1.267A2.675 2.675 0 0 0 17.642 17a2.58 2.58 0 0 0 .525-.642c.25-.434.375-.917.366-1.409a2.686 2.686 0 0 0-.833-1.9 2.649 2.649 0 0 0-1.867-.758Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M15.375 16.458a.626.626 0 0 1-.433-.175l-.842-.8a.621.621 0 0 1-.017-.883.621.621 0 0 1 .884-.017l.408.392 1.308-1.267a.621.621 0 0 1 .884.017.621.621 0 0 1-.017.883l-1.742 1.684a.673.673 0 0 1-.433.166ZM10 11.083A.622.622 0 0 1 9.683 11L2.325 6.742a.626.626 0 0 1 .625-1.084l7.042 4.075 7-4.05a.622.622 0 0 1 .85.225c.175.3.066.684-.226.859L10.308 11a.656.656 0 0 1-.308.083Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 18.634a.63.63 0 0 1-.625-.625V10.45A.63.63 0 0 1 10 9.825a.63.63 0 0 1 .625.625v7.559a.63.63 0 0 1-.625.625Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 18.959c-.733 0-1.467-.159-2.033-.484l-4.45-2.466c-1.209-.667-2.159-2.275-2.159-3.659V7.634c0-1.384.95-2.984 2.159-3.659l4.45-2.466c1.133-.642 2.916-.642 4.058 0l4.45 2.466c1.208.667 2.158 2.275 2.158 3.659v4.716c0 .084 0 .15-.016.234-.042.216-.2.4-.409.466a.64.64 0 0 1-.616-.116c-.959-.834-2.442-.867-3.45-.059a2.686 2.686 0 0 0-.625 3.5c.066.117.133.209.208.3a.615.615 0 0 1-.175.95l-1.525.842c-.567.333-1.292.492-2.025.492Zm0-16.667c-.517 0-1.042.108-1.417.317l-4.45 2.466c-.808.442-1.508 1.642-1.508 2.559v4.716c0 .917.708 2.117 1.508 2.559l4.45 2.466c.759.425 2.084.425 2.842 0l.933-.516a3.893 3.893 0 0 1-.475-1.875c0-1.217.542-2.342 1.484-3.092 1.133-.908 2.75-1.1 4.025-.558V7.617c0-.917-.709-2.117-1.509-2.558l-4.45-2.467c-.391-.192-.916-.3-1.433-.3Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
