'use client';

interface LoginHistoryProps {
  isSelected?: boolean;
}

export function LoginHistory({ isSelected }: LoginHistoryProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.15 18.958c-.083 0-.175-.008-.25-.017l-1.808-.25c-.867-.116-1.65-.891-1.784-1.775l-.25-1.825c-.083-.583.167-1.341.584-1.766L5.3 9.666a6.902 6.902 0 0 1 1.833-6.591c2.7-2.692 7.092-2.7 9.8 0a6.882 6.882 0 0 1 2.025 4.9c0 1.85-.716 3.591-2.025 4.9-1.75 1.733-4.241 2.416-6.591 1.816L6.675 18.35c-.35.366-.975.608-1.525.608ZM12.025 2.3c-1.458 0-2.908.55-4.017 1.658a5.67 5.67 0 0 0-1.416 5.708.621.621 0 0 1-.159.625l-3.916 3.917c-.142.142-.259.508-.234.7l.25 1.825c.05.317.392.675.709.717l1.816.25c.2.033.567-.084.709-.225L9.7 13.55a.622.622 0 0 1 .625-.15c2.008.633 4.2.091 5.7-1.409a5.654 5.654 0 0 0 1.658-4.016 5.64 5.64 0 0 0-1.658-4.017 5.606 5.606 0 0 0-4-1.658Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M7.658 17.117a.618.618 0 0 1-.441-.184L5.3 15.017a.629.629 0 0 1 0-.884.629.629 0 0 1 .883 0L8.1 16.05a.629.629 0 0 1 0 .883.618.618 0 0 1-.442.184Zm4.425-7.325a1.878 1.878 0 0 1-1.875-1.875c0-1.033.842-1.875 1.875-1.875 1.034 0 1.875.842 1.875 1.875a1.878 1.878 0 0 1-1.875 1.875Zm0-2.5a.63.63 0 0 0-.625.625.63.63 0 0 0 .625.625.63.63 0 0 0 .625-.625.63.63 0 0 0-.625-.625Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
