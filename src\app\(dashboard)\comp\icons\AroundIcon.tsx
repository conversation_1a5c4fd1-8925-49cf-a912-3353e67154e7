import * as React from "react";
import { SVGProps } from "react";
const AroundIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M19.6666 17.1667H6.33325C6.05992 17.1667 5.83325 17.3933 5.83325 17.6667C5.83325 17.94 6.05992 18.1667 6.33325 18.1667H19.6666C19.9399 18.1667 20.1666 17.94 20.1666 17.6667C20.1666 17.3933 19.9399 17.1667 19.6666 17.1667Z"
      fill="white"
    />
    <path
      opacity={0.4}
      d="M16.3333 4.33333H9.66667C7.66667 4.33333 7 5.52667 7 7V17.6667H19V7C19 5.52667 18.3333 4.33333 16.3333 4.33333Z"
      fill="white"
    />
    <path
      d="M14.3734 13H11.6201C11.2801 13 10.9934 13.28 10.9934 13.6267V17.6667H14.9934V13.6267C15.0001 13.28 14.7201 13 14.3734 13Z"
      fill="white"
    />
    <path
      d="M14.6666 8.16667H13.4999V7C13.4999 6.72667 13.2733 6.5 12.9999 6.5C12.7266 6.5 12.4999 6.72667 12.4999 7V8.16667H11.3333C11.0599 8.16667 10.8333 8.39333 10.8333 8.66667C10.8333 8.94 11.0599 9.16667 11.3333 9.16667H12.4999V10.3333C12.4999 10.6067 12.7266 10.8333 12.9999 10.8333C13.2733 10.8333 13.4999 10.6067 13.4999 10.3333V9.16667H14.6666C14.9399 9.16667 15.1666 8.94 15.1666 8.66667C15.1666 8.39333 14.9399 8.16667 14.6666 8.16667Z"
      fill="white"
    />
  </svg>
);
export default AroundIcon;
