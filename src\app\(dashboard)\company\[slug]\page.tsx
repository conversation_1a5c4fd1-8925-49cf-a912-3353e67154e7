"use client";

import React from 'react';
import { useParams } from 'next/navigation';
import { DasboardOverview } from '@/app/(dashboard)/comp/components/DashboardOverview';
import { useUser } from '@/app/(auth)/(onboarding)/misc';

// Helper function to convert slug back to company name
const slugToCompanyName = (slug: string): string => {
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Helper function to find exact company name from available companies
const findExactCompanyName = (slug: string, companies: any[]): string | null => {
  if (!companies || companies.length === 0) return null;

  // Create a slug for each company and compare
  const targetSlug = slug.toLowerCase();

  const exactMatch = companies.find(company => {
    if (!company.company_name) return false;

    const companySlug = company.company_name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .trim();

    return companySlug === targetSlug;
  });

  return exactMatch ? exactMatch.company_name : slugToCompanyName(slug);
};

export default function CompanyPage() {
  const params = useParams();
  const slug = params?.slug as string;
  const { data: userData, isLoading: loadingCompanies } = useUser();

  if (!slug) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-white">Invalid company page</p>
      </div>
    );
  }

  // Find the exact company name from available companies
  const companyName = userData?.data?.companies
    ? findExactCompanyName(slug, userData.data.companies)
    : slugToCompanyName(slug);

  if (loadingCompanies) {
    return (
      <div className="min-h-screen bg-[#0a1628] p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <div className="h-8 bg-gray-600 rounded mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2 animate-pulse"></div>
          </div>
          <div className="h-96 bg-gray-600 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a1628] p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            {companyName} Dashboard
          </h1>
          <p className="text-gray-400">
            Overview and analytics for {companyName}
          </p>
        </div>

        {/* Pass the company name to DashboardOverview */}
        <DasboardOverview companyName={companyName} />
      </div>
    </div>
  );
}
