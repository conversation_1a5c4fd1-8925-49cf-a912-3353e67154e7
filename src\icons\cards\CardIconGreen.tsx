import * as React from 'react';
import { SVGProps } from 'react';

const CardIconGreen = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    height={34}
    width={34}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={17} cy={17} fill="#EBFFF2" r={17} />
    <g clipPath="url(#a)" fill="#037929">
      <path d="M24.552 13.595v1.329H11.006v-1.33c0-.211.172-.383.384-.383h12.777a.384.384 0 0 1 .384.384ZM11.006 16.875v5.67a.383.383 0 0 0 .384.384h12.777a.384.384 0 0 0 .384-.384v-5.67H11.006Zm6.57 5.132h-5.457v-.36h5.457v.36Zm5.747-.384a.384.384 0 0 1-.384.384h-1.76a.384.384 0 0 1-.383-.384v-.873a.384.384 0 0 1 .384-.384h1.793c.2.015.356.182.355.384l-.005.873ZM22.61 10.914H9.834a.384.384 0 0 0-.384.384v1.329h13.546v-1.329a.384.384 0 0 0-.384-.384Z" />
      <path d="M11.006 14.912h11.987v-.346H9.447v5.678a.384.384 0 0 0 .384.384h10.985a.384.384 0 0 1 .364-.265h1.794a.421.421 0 0 0 .019-.121V16.87H11.006v-1.958Zm5.017 4.8h-5.462v-.367h5.462v.367Zm5.747-.384a.384.384 0 0 1-.384.384H19.62a.384.384 0 0 1-.384-.384v-.88c0-.211.172-.383.384-.383h1.765a.384.384 0 0 1 .384.384v.88Z" />
    </g>
    <defs>
      <clipPath id="a">
        <path d="M9 11h16v12H9z" fill="#fff" />
      </clipPath>
    </defs>
  </svg>
);
export default CardIconGreen;
