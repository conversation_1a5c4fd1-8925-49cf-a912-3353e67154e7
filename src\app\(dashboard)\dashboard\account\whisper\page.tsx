import React from 'react'
import { Button } from "@/components/core";
import { cn } from "@/utils/classNames";
import TransactionsTable from '@/app/(dashboard)/comp/components/transactions/table/TransactionsTable';

export default function whisper() {
    const cardDetails = [{
        title: 'Overall Balance',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'Total Inflow',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'Total Outflow',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'WEMA Balance',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'VFD Balance',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'Fidelity Balance',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'Zenith Balance',
        amount: '300,000,000',
        percentage: '+10%',
        increase: 'Increase from last month'
    },{
        title: 'N/A',
        amount: '0',
        percentage: '0%',
        // increase: 'Increase from last month'
    }
    ]
    
    return (
    <div className=' bg-[#0b1739] rounded-10'>
            <div className='flex justify-between items-center text-white  px-6 pt-6'>
            <span className='text-xl font-medium'>Whisper SMS</span>
            <Button className={cn(" bg-[#122251] px-5 py-2.5 rounded-lg","font-display text-xs font-medium")}>
                Export
            </Button>
            </div>
            <div className='grid justify-items-center grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-y-4 gap-x-4  py-3 px-6  '>{
                 cardDetails.map((card, index) => (
                    <div className='bg-[#122251] h-[117px] rounded-10 w-full'>
                    <h1 className='pt-4 px-4 font-normal text-xs text-white '>{card.title}</h1>
                    <h1 className='py-2 px-4 font-bold text-[22px] text-white'>&#8358; {card.amount}</h1>
                   
                        <h3 className='pb- px-4 font-normal text-xs text-white'>
                        <span className=' pr-2 text-xs text-[#00ff31]'>{card.percentage}</span>
                            {card.increase}
                        </h3>
                 
                </div>
                  ))}
           
            
                  </div>
    
                  <TransactionsTable />
                  <div className="relative p-2">
                
              </div>
              </div>    
    )
    }
    
