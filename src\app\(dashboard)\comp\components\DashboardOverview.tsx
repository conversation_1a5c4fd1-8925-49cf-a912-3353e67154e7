import { Button } from "@/components/core";
import { cn } from "@/utils/classNames";
import { useAccountDetails } from '@/app/(auth)/(onboarding)/misc/api/getAccountsDetails';
import { useCompany } from '@/contexts/CompanyContext';

import React from 'react';
// import { useUser } from '@/app/(auth)/(onboarding)/misc';

interface DashboardOverviewProps {
  companyName?: string;
}

export const DasboardOverview = ({ companyName }: DashboardOverviewProps) => {
  const { selectedCompany } = useCompany();

  // Use selectedCompany from context if available, otherwise use prop
  const activeCompany = selectedCompany || companyName;

  const { data: userData, isLoading } = useAccountDetails(activeCompany);
  console.log(userData, "userData for company:", activeCompany)


  //     const cardDetails = [{
  //         title: 'Overall Balance',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Total Inflow',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Total Outflow',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Libertypay Balance',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Paybox Balance',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Seeds & Pennies Balance',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Libertylife Balance',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     },{
  //         title: 'Whisper SMS Balance',
  //         amount: '300,000,000',
  //         percentage: '+10%',
  //         increase: 'Increase from last month'
  //     }
  // ]
  // Helper function to format numbers with commas
  const formatNumber = (num: number | undefined) => {
    if (num === undefined || num === null || isNaN(num)) return '0';
    return num?.toLocaleString()
  }

  // Helper function to calculate percentage change (you can modify this logic based on your needs)
  const calculatePercentage = (current: number | undefined, previous = 0) => {
    if (current === undefined || current === null || isNaN(current)) return "0%";
    if (previous === 0) return current > 0 ? "+100%" : "0%"
    const change = ((current - previous) / previous) * 100
    return change >= 0 ? `+${change.toFixed(1)}%` : `${change.toFixed(1)}%`
  }

  // Generate card details from API data
  const generateCardDetails = () => {
    if (!userData?.data) return []

    const { overall, companies } = userData.data

    // Debug logging to understand the API response structure
    console.log("API Response for company:", activeCompany);
    console.log("Overall data:", overall);
    console.log("Companies data:", companies);
    console.log("Companies type:", typeof companies);
    console.log("Is companies array:", Array.isArray(companies));

    const cards = [
      {
        title: "Overall Balance",
        amount: formatNumber(overall?.balance),
        percentage: calculatePercentage(overall?.balance),
        increase: "Current balance",
      },
      {
        title: "Total Inflow",
        amount: formatNumber(overall?.cash_flow.total_inflow),
        percentage: calculatePercentage(overall?.cash_flow.total_inflow),
        increase: "Total incoming funds",
      },
      {
        title: "Total Outflow",
        amount: formatNumber(overall?.cash_flow.total_outflow),
        percentage: calculatePercentage(overall?.cash_flow.total_outflow),
        increase: "Total outgoing funds",
      },
    ]

    // Add company-specific cards only if companies array exists and is not empty
    let companyCards: Array<{
      title: string;
      amount: string;
      percentage: string;
      increase: string;
    }> = [];

    if (companies && Array.isArray(companies) && companies.length > 0) {
      companyCards = companies
        .filter((company) => company.company_name && company.company_name.trim() !== "")
        .map((company) => ({
          title: `${company.company_name} Balance`,
          amount: formatNumber(company.balance),
          percentage: calculatePercentage(company.balance),
          increase: `Inflow: ₦${formatNumber(company.cash_flow.total_inflow)} | Outflow: ₦${formatNumber(company.cash_flow.total_outflow)}`,
        }));
    }

    // If a specific company is selected, show only the overall cards (which represent that company's data)
    if (activeCompany) {
      return cards;
    }

    // If no specific company is selected, show all cards including company-specific ones
    return [...cards, ...companyCards]
  }

  const cardDetails = generateCardDetails()

  // Loading state
  if (isLoading) {
    return (
      <div className="pb-3 bg-[#0b1739] rounded-10">
        <div className="flex justify-between items-center text-white px-6 pt-6">
          <div>
            <span className="text-xl font-medium">
              {activeCompany ? `${activeCompany} Overview` : 'Overview'}
            </span>
            {activeCompany && (
              <p className="text-sm text-gray-400 mt-1">
                Loading data for {activeCompany}...
              </p>
            )}
          </div>
          <Button
            className={cn("bg-[#122251] px-5 py-2.5 rounded-lg w-[104px] h-[44px]", "font-display text-xs font-medium")}
          >
            Export
          </Button>
        </div>
        <div className="grid justify-items-center grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-y-4 gap-x-4 py-3 px-6">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-[#122251] rounded-10 w-full pb-[21.5px] py-2 animate-pulse">
              <div className="pt-4 px-4 h-4 bg-gray-600 rounded mb-2"></div>
              <div className="py-2 px-4 h-6 bg-gray-600 rounded mb-2"></div>
              <div className="px-4 h-3 bg-gray-600 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (!userData?.data) {
    return (
      <div className="pb-3 bg-[#0b1739] rounded-10">
        <div className="flex justify-between items-center text-white px-6 pt-6">
          <span className="text-xl font-medium">Overview</span>
          <Button
            className={cn("bg-[#122251] px-5 py-2.5 rounded-lg w-[104px] h-[44px]", "font-display text-xs font-medium")}
          >
            Export
          </Button>
        </div>
        <div className="flex items-center justify-center py-12">
          <p className="text-white text-center">No data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="pb-3 bg-[#0b1739] rounded-10">
      <div className="flex justify-between items-center text-white px-6 pt-6">
        <div>
          <span className="text-xl font-medium">
            {activeCompany ? `${activeCompany} Overview` : 'Overview'}
          </span>
          {activeCompany && (
            <p className="text-sm text-gray-400 mt-1">
              Showing data for {activeCompany}
            </p>
          )}
        </div>
        <Button
          className={cn("bg-[#122251] px-5 py-2.5 rounded-lg w-[104px] h-[44px]", "font-display text-xs font-medium")}
        >
          Export
        </Button>
      </div>
      <div className="grid justify-items-center grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-y-4 gap-x-4 py-3 px-6">
        {cardDetails.map((card, index) => (
          <div
            key={`${card.title}-${index}`}
            className="bg-[#122251] rounded-10 w-full pb-[21.5px] py-2 hover:bg-[#1a2a5a] transition-colors duration-200"
          >
            <h1 className="pt-4 px-4 font-normal text-xs text-white">{card.title}</h1>
            <h1 className="py-2 px-4 font-bold text-[22px] text-white">&#8358; {card.amount}</h1>
            <h3 className="px-4 font-normal text-xs text-white">
              <span className="pr-2 text-xs text-[#00ff31]">{card.percentage}</span>
              {card.increase}
            </h3>
          </div>
        ))}
      </div>
    </div>
  );
};

