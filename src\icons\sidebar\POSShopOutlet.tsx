'use client';

interface POSShopOutletProps {
  isSelected?: boolean;
}

export function POSShopOutlet({ isSelected }: POSShopOutletProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.5 18.959h-5c-3.675 0-4.792-1.117-4.792-4.792V5.834c0-3.675 1.117-4.792 4.792-4.792h5c3.675 0 4.792 1.117 4.792 4.792v8.333c0 3.675-1.117 4.792-4.792 4.792Zm-5-16.667c-2.983 0-3.542.567-3.542 3.542v8.333c0 2.975.559 3.542 3.542 3.542h5c2.983 0 3.542-.567 3.542-3.542V5.834c0-2.975-.559-3.542-3.542-3.542h-5Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M11.667 5.208H8.333a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h3.334a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625ZM10 16.55a1.917 1.917 0 1 1 0-3.834 1.917 1.917 0 0 1 0 3.834Zm0-2.591c-.367 0-.667.3-.667.666 0 .367.3.667.667.667.367 0 .667-.3.667-.667 0-.367-.3-.666-.667-.666Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
