import * as React from 'react';
import { SVGProps } from 'react';

const CardIconBlue = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    height={34}
    width={34}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={17} cy={17} fill="#F1F8FF" r={17} />
    <g clipPath="url(#a)" fill="#073D9F">
      <path d="M24.661 11.954c-4.3-.538-6.758-3.034-6.854-3.13a1.144 1.144 0 0 0-.903-.345c-.268.019-.518.153-.71.345-.02.02-2.477 2.573-6.835 3.13-.595.077-1.056.614-.999 1.21.71 9.887 7.872 13.305 8.18 13.44a.997.997 0 0 0 .46.096c.154 0 .327-.039.461-.097.307-.134 7.469-3.552 8.18-13.459a1.15 1.15 0 0 0-.98-1.19Zm-4.531 8.025c0 .461-.384.845-.845.845h-4.589a.852.852 0 0 1-.845-.845v-3.014c0-.46.384-.845.845-.845h.25v-1.325c0-1.133.921-2.035 2.035-2.035s2.035.922 2.035 2.035v1.325h.288c.461 0 .845.384.845.845v3.014h-.02Z" />
      <path d="M18.152 14.816c0-.634-.519-1.152-1.152-1.152-.634 0-1.152.518-1.152 1.152v1.325h2.304v-1.325ZM17.73 17.522l-.96.96-.48-.48a.452.452 0 0 0-.634 0 .42.42 0 0 0 0 .633l.806.807a.409.409 0 0 0 .307.134c.116 0 .23-.038.308-.134l1.267-1.268a.452.452 0 0 0 0-.633.414.414 0 0 0-.615-.02Z" />
    </g>
    <defs>
      <clipPath id="a">
        <path d="M8 8h18v19H8z" fill="#fff" />
      </clipPath>
    </defs>
  </svg>
);
export default CardIconBlue;
