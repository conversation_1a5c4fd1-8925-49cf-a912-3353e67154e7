'use client';

import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query';
import * as React from 'react';
// import { UnauthorizedModalProvider } from '@/contexts/ModalContext';
import type { AxiosError } from 'axios';

interface ReactQueryProviderProps {
  children: React.ReactNode;
}

const isAxios403Error = (error: unknown): error is AxiosError =>
  !!(error && typeof error === 'object' && 'response' in error &&
    (error as AxiosError).response?.status === 403);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // avoid infinite retries on 403
      retry: (failureCount, error: unknown) =>
        !isAxios403Error(error) && failureCount < 3,
    },
    mutations: {
      retry: (failureCount, error: unknown) =>
        !isAxios403Error(error) && failureCount < 3,
    },
  },
  queryCache: new QueryCache({
    onError: (error: unknown) => {
      if (isAxios403Error(error)) {
        // extract the message/payload from the response
        const detail = error.response?.data ?? error.message;
        window.dispatchEvent(
          new CustomEvent('api:unauthorized', { detail })
        );
      }
    },
  }),
  mutationCache: new MutationCache({
    onError: (error: unknown) => {
      if (isAxios403Error(error)) {
        const detail = error.response?.data ?? error.message;
        window.dispatchEvent(
          new CustomEvent('api:unauthorized', { detail })
        );
      }
    },
  }),
});


export function ReactQueryProvider({ children }: ReactQueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
  
        {children}

    </QueryClientProvider>
  );
}