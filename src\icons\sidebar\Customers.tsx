'use client';

interface CustomersProps {
  isSelected?: boolean;
}

export function Customers({ isSelected }: CustomersProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.633 9.684h-.067a.453.453 0 0 0-.15 0C5 9.609 3.175 7.709 3.175 5.367A4.332 4.332 0 0 1 7.5 1.042a4.332 4.332 0 0 1 4.325 4.325 4.31 4.31 0 0 1-4.167 4.317h-.025ZM7.5 2.292a3.084 3.084 0 0 0-3.075 3.075c0 1.667 1.3 3.008 2.958 3.067.05-.009.158-.009.267 0a3.07 3.07 0 0 0 2.925-3.067A3.084 3.084 0 0 0 7.5 2.292Zm6.283 7.499c-.025 0-.05 0-.075-.008-.342.033-.692-.208-.725-.55-.033-.342.175-.65.517-.692.1-.008.208-.008.3-.008a2.289 2.289 0 0 0 2.166-2.292 2.29 2.29 0 0 0-2.291-2.291.617.617 0 0 1-.625-.617.63.63 0 0 1 .625-.625 3.55 3.55 0 0 1 3.541 3.542c0 1.916-1.5 3.466-3.408 3.541h-.025Zm-6.142 9c-1.633 0-3.275-.416-4.516-1.25-1.159-.766-1.792-1.816-1.792-2.958 0-1.142.633-2.2 1.792-2.975 2.5-1.658 6.55-1.658 9.033 0 1.15.767 1.792 1.817 1.792 2.958 0 1.142-.634 2.2-1.792 2.975-1.25.834-2.883 1.25-4.517 1.25Zm-3.825-6.133c-.8.533-1.233 1.217-1.233 1.933 0 .709.442 1.392 1.233 1.917 2.075 1.392 5.575 1.392 7.65 0 .8-.533 1.234-1.217 1.234-1.933 0-.709-.442-1.392-1.234-1.917-2.075-1.383-5.575-1.383-7.65 0Zm11.467 4.634c-.292 0-.55-.2-.608-.5a.634.634 0 0 1 .483-.742c.525-.108 1.008-.317 1.383-.608.475-.359.734-.809.734-1.284s-.259-.925-.725-1.275c-.367-.283-.825-.483-1.367-.608a.63.63 0 0 1-.475-.75.63.63 0 0 1 .75-.475c.717.158 1.342.442 1.85.833.775.584 1.217 1.409 1.217 2.275 0 .867-.45 1.692-1.225 2.284a4.52 4.52 0 0 1-1.884.833c-.05.017-.091.017-.133.017Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
