import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1677_2985)">
      <path
        d="M8 0C6.95385 0 6.15385 0.8 6.15385 1.84615V5.53846C6.15385 6.58462 6.95385 7.38462 8 7.38462H11.6923L14.1538 9.84615V7.38462C15.2 7.38462 16 6.58462 16 5.53846V1.84615C16 0.8 15.2 0 14.1538 0H8ZM10.5772 1.84615H11.6345L12.6732 5.53846H11.7502L11.4997 4.61538H10.5766L10.3465 5.53846H9.53846L10.5772 1.84615ZM11.0769 2.46154C11.0154 2.70769 10.9465 3.008 10.8843 3.19262L10.712 4H11.4425L11.2689 3.192C11.1465 3.008 11.0769 2.70769 11.0769 2.46154ZM1.84615 6.15385C0.8 6.15385 0 6.95385 0 8V11.6923C0 12.7385 0.8 13.5385 1.84615 13.5385V16L4.30769 13.5385H8C9.04615 13.5385 9.84615 12.7385 9.84615 11.6923V8H8C6.83077 8 5.904 7.2 5.59631 6.15385H1.84615ZM4.67323 7.94215C5.71938 7.94215 6.21169 8.80369 6.21169 9.78831C6.21169 10.6498 5.91569 11.1963 5.42338 11.4425C5.66954 11.5655 5.96123 11.6308 6.26892 11.6923L6.03877 12.3077C5.608 12.1846 5.16123 11.9926 4.73046 11.8074C4.66892 11.7458 4.56123 11.7502 4.49969 11.7502C3.76123 11.6886 3.07692 11.0769 3.07692 9.84615C3.07692 8.8 3.68862 7.94215 4.67323 7.94215ZM4.67323 8.61539C4.18092 8.61539 3.94215 9.16923 3.94215 9.84615C3.94215 10.5846 4.18092 11.0769 4.67323 11.0769C5.16554 11.0769 5.42277 10.5231 5.42277 9.84615C5.42277 9.16923 5.16554 8.61539 4.67323 8.61539Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_1677_2985">
        <rect width={16} height={16} fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export default SVGComponent;
