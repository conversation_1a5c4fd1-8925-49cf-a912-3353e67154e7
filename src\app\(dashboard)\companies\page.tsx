"use client";

import React from 'react';
import Link from 'next/link';
import { useUser } from '@/app/(auth)/(onboarding)/misc';
import { Button } from "@/components/core";
import { cn } from "@/utils/classNames";

// Helper function to create URL-friendly slug from company name
const createCompanySlug = (companyName: string) => {
  return companyName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .trim();
};

// Helper function to format numbers with commas
const formatNumber = (num: number) => {
  return num?.toLocaleString();
};

export default function CompaniesPage() {
  const { data: userData, isLoading } = useUser();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0a1628] p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-white mb-2">Companies</h1>
            <p className="text-gray-400">Overview of all companies</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-[#0b1739] rounded-10 p-6 animate-pulse">
                <div className="h-6 bg-gray-600 rounded mb-4"></div>
                <div className="h-8 bg-gray-600 rounded mb-2"></div>
                <div className="h-4 bg-gray-600 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!userData?.data?.companies) {
    return (
      <div className="min-h-screen bg-[#0a1628] p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-white mb-2">Companies</h1>
            <p className="text-gray-400">Overview of all companies</p>
          </div>

          <div className="flex items-center justify-center py-12">
            <p className="text-white text-center">No companies available</p>
          </div>
        </div>
      </div>
    );
  }

  const validCompanies = userData.data.companies.filter(
    (company) => company.company_name && company.company_name.trim() !== ""
  );

  return (
    <div className="min-h-screen bg-[#0a1628] p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Companies</h1>
          <p className="text-gray-400">
            Overview of all companies ({validCompanies.length} total)
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {validCompanies.map((company, index) => (
            <Link
              key={`${company.company_name}-${index}`}
              href={`/dashboard/company/${createCompanySlug(company.company_name)}`}
              className="block"
            >
              <div className="bg-[#0b1739] rounded-10 p-6 hover:bg-[#132863] transition-colors duration-200 cursor-pointer border border-transparent hover:border-[#1a2a5a]">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {company.company_name}
                </h3>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Balance:</span>
                    <span className="text-white font-medium">
                      ₦{formatNumber(company.balance)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Total Inflow:</span>
                    <span className="text-green-400 font-medium">
                      ₦{formatNumber(company.cash_flow.total_inflow)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Total Outflow:</span>
                    <span className="text-red-400 font-medium">
                      ₦{formatNumber(company.cash_flow.total_outflow)}
                    </span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-600">
                  <Button className="w-full bg-[#122251] hover:bg-[#1a2a5a] text-white text-sm py-2 px-4 rounded-lg transition-colors duration-200">
                    View Details →
                  </Button>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
