import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    fill="none"
    height={20}
    viewBox="0 0 25 20"
    width={25}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      clipRule="evenodd"
      d="M16.5222 7.64281C17.7806 8.90117 17.8225 10.9153 16.6481 12.224L16.5222 12.3569L13.0894 15.5891C12.7639 15.9145 12.2363 15.9145 11.9108 15.5891C11.6104 15.2887 11.5873 14.816 11.8415 14.4891L11.9108 14.4106L15.3437 11.1783C15.9603 10.5617 15.9928 9.58216 15.4411 8.92734L15.3437 8.82133L11.9108 5.58909C11.5854 5.26366 11.5854 4.73602 11.9108 4.41058C12.2112 4.11018 12.6839 4.08707 13.0109 4.34126L13.0894 4.41058L16.5222 7.64281Z"
      fill="black"
      fillRule="evenodd"
    />
    <g opacity={0.3}>
      <path
        clipRule="evenodd"
        d="M11.5222 7.64281C12.7806 8.90117 12.8225 10.9153 11.6481 12.224L11.5222 12.3569L8.08936 15.5891C7.76392 15.9145 7.23628 15.9145 6.91085 15.5891C6.61044 15.2887 6.58733 14.816 6.84152 14.4891L6.91085 14.4106L10.3437 11.1783C10.9603 10.5617 10.9928 9.58216 10.4411 8.92734L10.3437 8.82133L6.91085 5.58909C6.58541 5.26366 6.58541 4.73602 6.91085 4.41058C7.21125 4.11018 7.68394 4.08707 8.01085 4.34126L8.08936 4.41058L11.5222 7.64281Z"
        fill="black"
        fillRule="evenodd"
      />
    </g>
  </svg>
);
export default SVGComponent;
