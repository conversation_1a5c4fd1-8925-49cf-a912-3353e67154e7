'use client';

interface LedgerProps {
  isSelected?: boolean;
}

export function Ledger({ isSelected }: LedgerProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.783 18.284a.616.616 0 0 1-.5-.25l-.841-1.125c-.175-.234-.409-.367-.659-.384-.258-.016-.5.1-.7.309-1.208 1.291-2.125 1.183-2.566 1.016-.45-.175-1.209-.75-1.209-2.6V5.867c0-3.7 1.067-4.825 4.559-4.825h6.291c3.492 0 4.559 1.125 4.559 4.825v3.55a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625v-3.55c0-3.008-.525-3.575-3.309-3.575H6.867c-2.784 0-3.309.567-3.309 3.575v9.383c0 .875.217 1.359.417 1.434.15.058.558-.025 1.192-.7.458-.484 1.058-.742 1.683-.709.617.034 1.2.359 1.592.884l.85 1.125a.625.625 0 0 1-.509 1Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M13.333 6.458H6.667a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h6.666a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625ZM12.5 9.792h-5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm-.15 8.358c-.317 0-.617-.116-.833-.333a1.186 1.186 0 0 1-.317-1.025l.158-1.125a1.65 1.65 0 0 1 .425-.85l2.95-2.95c.4-.4.792-.608 1.217-.65.517-.05 1.033.167 1.517.65.483.483.7.992.65 1.517-.042.416-.259.816-.65 1.216l-2.95 2.95a1.65 1.65 0 0 1-.85.425l-1.125.159c-.067.008-.125.016-.192.016Zm3.742-5.691h-.025c-.117.008-.275.116-.45.291l-2.95 2.95a.317.317 0 0 0-.067.142l-.15 1.042 1.042-.15a.465.465 0 0 0 .141-.075l2.95-2.95c.175-.184.284-.334.292-.45.017-.167-.15-.367-.292-.509-.133-.133-.325-.291-.491-.291Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M16.6 15.208a.536.536 0 0 1-.167-.025 3.314 3.314 0 0 1-2.283-2.284.633.633 0 0 1 .433-.775.62.62 0 0 1 .767.434 2.054 2.054 0 0 0 1.417 1.416.63.63 0 0 1-.167 1.234Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
