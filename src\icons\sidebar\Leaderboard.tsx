'use client';

interface LeaderboardProps {
  isSelected?: boolean;
}

export function Leaderboard({ isSelected }: LeaderboardProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 13.959c-.333 0-.667-.025-.983-.084a6.457 6.457 0 0 1-4.392-2.783A6.472 6.472 0 0 1 3.542 7.5c0-3.558 2.9-6.458 6.458-6.458 3.558 0 6.458 2.9 6.458 6.458a6.473 6.473 0 0 1-1.083 3.592 6.5 6.5 0 0 1-4.417 2.792c-.291.05-.625.075-.958.075Zm0-11.667A5.21 5.21 0 0 0 4.792 7.5c0 1.042.3 2.042.866 2.892a5.211 5.211 0 0 0 3.55 2.242 4.404 4.404 0 0 0 1.55 0 5.219 5.219 0 0 0 3.575-2.25 5.19 5.19 0 0 0 .867-2.892c.008-2.867-2.325-5.2-5.2-5.2Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M5.392 18.825a1.6 1.6 0 0 1-.342-.042A1.438 1.438 0 0 1 3.967 17.7l-.292-1.225a.218.218 0 0 0-.158-.159l-1.375-.325a1.45 1.45 0 0 1-1.067-1.016c-.142-.509 0-1.059.375-1.434l3.25-3.25a.628.628 0 0 1 .5-.183c.183.017.35.117.458.275a5.22 5.22 0 0 0 3.559 2.25 4.4 4.4 0 0 0 1.55 0 5.218 5.218 0 0 0 3.575-2.25.61.61 0 0 1 .458-.275.628.628 0 0 1 .5.183l3.25 3.25c.375.375.517.925.375 1.434a1.45 1.45 0 0 1-1.067 1.016l-1.375.325a.218.218 0 0 0-.158.159l-.292 1.225a1.438 1.438 0 0 1-1.083 1.083 1.43 1.43 0 0 1-1.45-.483L10 14.275l-3.5 4.033a1.445 1.445 0 0 1-1.108.517Zm-.317-7.134-2.742 2.742a.195.195 0 0 0-.05.208c.009.042.05.125.15.142l1.375.325a1.44 1.44 0 0 1 1.084 1.083l.291 1.225c.025.109.109.142.159.159a.21.21 0 0 0 .208-.067l3.192-3.675a6.473 6.473 0 0 1-3.667-2.142Zm6.183 2.134 3.192 3.666c.075.092.167.092.216.075.05-.008.125-.05.159-.158l.292-1.225A1.438 1.438 0 0 1 16.2 15.1l1.375-.325c.1-.025.142-.1.15-.142a.198.198 0 0 0-.05-.208l-2.742-2.742a6.494 6.494 0 0 1-3.675 2.142Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M11.575 10.742c-.217 0-.475-.059-.783-.242L10 10.025l-.792.467c-.725.433-1.2.183-1.375.058-.175-.125-.55-.5-.358-1.325l.2-.858-.667-.617c-.366-.366-.5-.808-.375-1.208.125-.4.492-.683 1.009-.767l.891-.15.425-.933C9.2 4.217 9.575 3.95 10 3.95c.425 0 .808.275 1.042.75l.491.984.825.1c.509.083.875.366 1.009.766.125.4-.009.842-.375 1.209l-.692.691.217.775c.191.825-.184 1.2-.359 1.325a.928.928 0 0 1-.583.192Zm-3.567-3.75.575.575c.267.267.4.717.317 1.083l-.158.667.666-.392a1.214 1.214 0 0 1 1.192 0l.667.392-.15-.667a1.223 1.223 0 0 1 .308-1.083L12 6.992l-.725-.125a1.24 1.24 0 0 1-.858-.633L10 5.417l-.417.833c-.15.309-.5.575-.85.634l-.725.108Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
