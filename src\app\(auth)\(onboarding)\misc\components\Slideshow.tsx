'use client';

import { motion } from 'framer-motion';
import * as React from 'react';

import { LinkButton } from '@/components/core/LinkButton';
import { shiftArrayItemsByOne } from '@/utils/arrays';
import { cn } from '@/utils/classNames';

const slideshowContent = [
  {
    heading: 'Finance',
    text: 'Money, like emotions, is something you must control to keep your life on the right track',
  },
  {
    heading: 'Assured interest',
    text: 'Earn incredibly high interest with ease by simply creating a plan and have us lend on your behave',
  },
  {
    heading: 'Fund dreams',
    text: 'Empower individuals with your funds by lending to them with our swift lending solution',
  },
  {
    heading: 'Lending Partner',
    text: 'Expand your income scream by becoming a seeds olending partner today and joining our lending community',
  },
];

export function Slideshow() {
  const [currentSlideshow, setCurrentSlideshow] =
    React.useState(slideshowContent);

  React.useEffect(() => {
    const interval = setInterval(() => {
      const shiftedSlideshow = shiftArrayItemsByOne(currentSlideshow);
      setCurrentSlideshow(shiftedSlideshow);
    }, 3500); // Change the active index every 5 seconds

    return () => clearInterval(interval);
  }, [currentSlideshow]);

  return (
    <div className="relative hidden md:block mx-auto w-full max-w-[32.375rem] shrink-0 overflow-hidden text-white md:h-screen-small md:max-w-none md:basis-1/2 md:pb-0 md:[@media(max-height:520px)]:overflow-y-auto">
      <header className="absolute top-0 z-10 hidden w-full items-center justify-between  px-6 py-11 md:flex xl:px-10">
        <svg fill="none" height="42" viewBox="0 0 76 42" width="76" xmlns="http://www.w3.org/2000/svg">
          <path d="M8.256 23.24C3.384 23.24 0.648 21.32 0.648 17.576V17.432H4.248V17.864C4.248 19.352 4.992 19.952 8.256 19.952C11.136 19.952 11.808 19.52 11.808 18.44C11.808 17.456 11.256 17.096 9.6 16.808L5.088 16.16C2.208 15.704 0.504 14.264 0.504 11.648C0.504 9.224 2.472 6.68 7.872 6.68C12.816 6.68 15.12 8.96 15.12 12.344V12.488H11.496V12.152C11.496 10.616 10.704 9.968 7.512 9.968C4.92 9.968 4.128 10.472 4.128 11.504C4.128 12.44 4.656 12.776 6 13.016L10.512 13.736C14.112 14.312 15.408 16.064 15.408 18.272C15.408 20.888 13.368 23.24 8.256 23.24ZM23.7381 23.24C19.7301 23.24 16.9941 21.272 16.9941 17C16.9941 13.16 19.7061 10.736 23.6661 10.736C27.6021 10.736 30.2181 12.8 30.2181 16.568C30.2181 17 30.1701 17.312 30.1221 17.72H20.3301C20.4261 19.568 21.2901 20.36 23.6181 20.36C25.7541 20.36 26.4981 19.808 26.4981 18.776V18.536H30.0981V18.8C30.0981 21.416 27.5541 23.24 23.7381 23.24ZM23.5941 13.544C21.4581 13.544 20.5461 14.264 20.3781 15.824H26.7621C26.6661 14.24 25.7061 13.544 23.5941 13.544ZM38.6087 23.24C34.6007 23.24 31.8647 21.272 31.8647 17C31.8647 13.16 34.5767 10.736 38.5367 10.736C42.4727 10.736 45.0887 12.8 45.0887 16.568C45.0887 17 45.0407 17.312 44.9927 17.72H35.2007C35.2967 19.568 36.1607 20.36 38.4887 20.36C40.6247 20.36 41.3687 19.808 41.3687 18.776V18.536H44.9687V18.8C44.9687 21.416 42.4247 23.24 38.6087 23.24ZM38.4647 13.544C36.3287 13.544 35.4167 14.264 35.2487 15.824H41.6327C41.5367 14.24 40.5767 13.544 38.4647 13.544ZM52.4713 23.24C48.7273 23.24 46.6633 20.792 46.6633 17C46.6633 13.16 48.7033 10.736 52.2793 10.736C55.1353 10.736 56.5993 12.152 57.0073 14.168H57.2233V6.92H60.8233V23H57.4633V19.664H57.2713C56.8153 22.04 55.2313 23.24 52.4713 23.24ZM50.3113 17C50.3113 19.184 51.3913 19.952 53.6953 19.952C55.9753 19.952 57.2233 19.16 57.2233 17.072V16.88C57.2233 14.792 55.9993 14.024 53.6953 14.024C51.3913 14.024 50.3113 14.792 50.3113 17ZM69.2801 23.24C65.2241 23.24 62.9921 21.656 62.9921 18.872V18.8H66.5921V19.016C66.5921 20.096 67.2641 20.384 69.3041 20.384C71.2241 20.384 71.6801 20.072 71.6801 19.352C71.6801 18.68 71.3201 18.488 69.9041 18.296L66.5201 17.888C64.1201 17.624 62.7761 16.544 62.7761 14.528C62.7761 12.416 64.5761 10.736 68.7041 10.736C72.6401 10.736 74.8721 12.224 74.8721 15.152V15.224H71.2721V15.08C71.2721 14.096 70.7921 13.592 68.5841 13.592C66.7841 13.592 66.3281 13.904 66.3281 14.672C66.3281 15.296 66.6641 15.56 68.2481 15.752L70.8161 16.064C74.0321 16.424 75.2321 17.504 75.2321 19.496C75.2321 21.752 73.0001 23.24 69.2801 23.24Z" fill="white" />
          <path d="M1.24 39H0.6V32.3H1.31V35.44H1.36C1.57 34.59 2.28 33.96 3.47 33.96C5.02 33.96 5.87 35.03 5.87 36.53C5.87 38.03 5.02 39.1 3.41 39.1C2.31 39.1 1.52 38.53 1.29 37.54H1.24V39ZM1.31 36.61C1.31 37.78 2.05 38.45 3.24 38.45C4.42 38.45 5.15 37.96 5.15 36.53C5.15 35.1 4.4 34.62 3.26 34.62C2.01 34.62 1.31 35.3 1.31 36.52V36.61ZM7.39258 40.7H6.77258V40.05H7.52258C8.02258 40.05 8.21258 39.9 8.40258 39.49L8.64258 38.99L6.19258 34.06H6.97258L8.42258 37L8.97258 38.21H9.03258L9.56258 36.99L10.9326 34.06H11.7126L9.03258 39.69C8.67258 40.45 8.21258 40.7 7.39258 40.7ZM19.1928 39H14.2328V32.3H14.9428V38.35H19.1928V39ZM20.7045 33.43H19.9945V32.3H20.7045V33.43ZM20.7045 39H19.9945V34.06H20.7045V39ZM22.5388 39H21.8988V32.3H22.6088V35.44H22.6588C22.8688 34.59 23.5788 33.96 24.7688 33.96C26.3188 33.96 27.1688 35.03 27.1688 36.53C27.1688 38.03 26.3188 39.1 24.7088 39.1C23.6088 39.1 22.8188 38.53 22.5888 37.54H22.5388V39ZM22.6088 36.61C22.6088 37.78 23.3488 38.45 24.5388 38.45C25.7188 38.45 26.4488 37.96 26.4488 36.53C26.4488 35.1 25.6988 34.62 24.5588 34.62C23.3088 34.62 22.6088 35.3 22.6088 36.52V36.61ZM30.3914 39.1C28.8014 39.1 27.7914 38.1 27.7914 36.53C27.7914 35.03 28.7914 33.96 30.3814 33.96C31.8314 33.96 32.8614 34.8 32.8614 36.27C32.8614 36.45 32.8414 36.6 32.8114 36.73H28.4614C28.5014 37.84 29.0714 38.51 30.3814 38.51C31.5414 38.51 32.0814 38.08 32.0814 37.36V37.29H32.7914V37.36C32.7914 38.39 31.7714 39.1 30.3914 39.1ZM30.3714 34.55C29.0914 34.55 28.5114 35.21 28.4614 36.29H32.1914C32.1914 36.24 32.1914 36.19 32.1914 36.14C32.1914 35.1 31.5314 34.55 30.3714 34.55ZM34.5131 39H33.8031V34.06H34.4431V35.41H34.4931C34.6431 34.62 35.2031 33.96 36.2331 33.96C37.3731 33.96 37.8731 34.8 37.8731 35.72V36.21H37.1631V35.83C37.1631 34.99 36.8131 34.58 35.9531 34.58C34.9531 34.58 34.5131 35.21 34.5131 36.32V39ZM41.7257 39H40.6557C39.6757 39 39.0457 38.59 39.0457 37.43V34.67H38.1657V34.06H39.0457V32.88H39.7657V34.06H41.7257V34.67H39.7657V37.47C39.7657 38.16 40.1057 38.35 40.8257 38.35H41.7257V39ZM43.1543 40.7H42.5343V40.05H43.2843C43.7843 40.05 43.9743 39.9 44.1643 39.49L44.4043 38.99L41.9543 34.06H42.7343L44.1843 37L44.7343 38.21H44.7943L45.3243 36.99L46.6943 34.06H47.4743L44.7943 39.69C44.4343 40.45 43.9743 40.7 43.1543 40.7ZM48.8686 39H48.1586V32.3H51.2386C52.6886 32.3 53.7086 33.13 53.7086 34.56C53.7086 36 52.6886 36.83 51.2386 36.83H48.8686V39ZM51.1786 32.95H48.8686V36.18H51.1786C52.3586 36.18 52.9886 35.7 52.9886 34.56C52.9886 33.44 52.3586 32.95 51.1786 32.95ZM55.847 39.1C54.877 39.1 54.217 38.64 54.217 37.84C54.217 37.03 54.887 36.68 55.807 36.58L58.157 36.32V35.94C58.157 34.98 57.737 34.6 56.657 34.6C55.597 34.6 55.037 34.98 55.037 35.85V35.89H54.327V35.85C54.327 34.81 55.187 33.96 56.707 33.96C58.207 33.96 58.847 34.82 58.847 35.91V39H58.207V37.67H58.157C57.867 38.58 56.997 39.1 55.847 39.1ZM54.927 37.79C54.927 38.29 55.257 38.55 56.007 38.55C57.207 38.55 58.157 38.02 58.157 36.83V36.79L56.027 37.03C55.287 37.1 54.927 37.28 54.927 37.79ZM60.6641 40.7H60.0441V40.05H60.7941C61.2941 40.05 61.4841 39.9 61.6741 39.49L61.9141 38.99L59.4641 34.06H60.2441L61.6941 37L62.2441 38.21H62.3041L62.8341 36.99L64.2041 34.06H64.9841L62.3041 39.69C61.9441 40.45 61.4841 40.7 60.6641 40.7Z" fill="white" fillOpacity="0.8" />
        </svg>

        <LinkButton
          className="hidden gap-2 px-5 py-2 text-base"
          href="/"
          size="unstyled"
          variant="white"
        >
          <span>
            <svg
              fill="none"
              height={24}
              viewBox="0 0 24 24"
              width={24}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m20.83 8.01-6.55-5.24C13 1.75 11 1.74 9.73 2.76L3.18 8.01c-.94.75-1.51 2.25-1.31 3.43l1.26 7.54C3.42 20.67 4.99 22 6.7 22h10.6c1.69 0 3.29-1.36 3.58-3.03l1.26-7.54c.18-1.17-.39-2.67-1.31-3.42Z"
                fill="#032282"
              />
              <path
                d="M12 18.75c-.41 0-.75-.34-.75-.75v-3c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75Z"
                fill="#fff"
              />
            </svg>
          </span>

          <span>Home</span>
        </LinkButton>
      </header>

      <aside className="md:h-screen">
        {currentSlideshow.map(({ heading, text }, index) => {
          const isLastItem = index + 1 === currentSlideshow.length;

          const isEven = (index + 1) % 2 === 0;

          return (
            <motion.div
              animate={{
                opacity: !isLastItem ? 0.2 : 1, // Fade in the current slide
                y: !isLastItem ? 20 : 0, // Animate from 20px below for current slide
              }}
              className={cn(
                'w-full p-6 md:flex md:flex-col md:justify-center xl:px-10 md:[@media(min-height:520px)]:h-1/4',
                !isLastItem && 'hidden md:block',
                index === 0 && 'bg-[#000000]/90',
                index === 1 && 'bg-[#00114B]/60'
              )}
              initial={{ opacity: 0.5, y: 20 }} // Initial state (hidden)
              key={heading}
              transition={{ duration: 1 }} // Animation duration
            >
              <div className={cn('w-max ', !isEven && 'ml-auto')}>
                <h2
                  className={cn(
                    'mb-4 font-clash text-3xl font-semibold leading-[78.5%] md:text-4xl',
                    isLastItem && 'xl:text-5xl'
                  )}
                >
                  {heading}
                </h2>
                <p className="leading-[123.5% max-w-[22.0625rem] font-clash">
                  {text}
                </p>
              </div>
            </motion.div>
          );
        })}
      </aside>
    </div>
  );
}
