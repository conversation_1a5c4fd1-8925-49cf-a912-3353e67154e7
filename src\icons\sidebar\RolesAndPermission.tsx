'use client';

interface RolesAndPermissionProps {
  isSelected?: boolean;
}

export function RolesAndPermission({ isSelected }: RolesAndPermissionProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 10.625a4.798 4.798 0 0 1-4.792-4.791A4.798 4.798 0 0 1 10 1.042a4.798 4.798 0 0 1 4.792 4.792A4.798 4.798 0 0 1 10 10.625Zm0-8.333a3.55 3.55 0 0 0-3.542 3.542A3.55 3.55 0 0 0 10 9.375a3.55 3.55 0 0 0 3.542-3.541A3.55 3.55 0 0 0 10 2.292ZM2.842 18.958a.63.63 0 0 1-.625-.625c0-3.558 3.491-6.458 7.783-6.458.842 0 1.667.108 2.467.333a.62.62 0 0 1 .433.767.62.62 0 0 1-.767.433A7.889 7.889 0 0 0 10 13.125c-3.6 0-6.533 2.333-6.533 5.208a.63.63 0 0 1-.625.625Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M15 18.959a3.935 3.935 0 0 1-3.383-1.925A3.843 3.843 0 0 1 11.042 15c0-1.216.541-2.341 1.483-3.091A3.962 3.962 0 0 1 18.958 15c0 .724-.2 1.433-.575 2.041a3.68 3.68 0 0 1-.791.933A3.827 3.827 0 0 1 15 18.96Zm0-6.667c-.617 0-1.2.208-1.692.6a2.686 2.686 0 0 0-.625 3.5A2.703 2.703 0 0 0 15 17.709a2.66 2.66 0 0 0 1.775-.675c.217-.184.4-.4.533-.634.267-.425.4-.908.4-1.4A2.714 2.714 0 0 0 15 12.292Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M14.525 16.45a.618.618 0 0 1-.442-.184l-.825-.825a.629.629 0 0 1 0-.883.629.629 0 0 1 .884 0l.4.4 1.333-1.233a.63.63 0 0 1 .883.033.63.63 0 0 1-.033.883l-1.775 1.642a.652.652 0 0 1-.425.167Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
