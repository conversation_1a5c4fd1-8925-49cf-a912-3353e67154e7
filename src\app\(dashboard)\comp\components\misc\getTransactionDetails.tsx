import { adminAxios } from "@/lib/axios";

import { useQuery } from '@tanstack/react-query';

export interface TransactionResponse {
    status: string
    status_code: number
    data: TransactionResponseEntity
    errors: any
}

export interface TransactionResponseEntity {
    transaction: TransactionResults
}

export interface TransactionResults {
    count: number
    next: string
    previous: string
    results: Result[]
    page_size: number
}

export interface Result {
    trnx_id: string
    trnx_date: string
    company_name: string
    reference: string
    balance_before: number
    balance_after: number
    amount: number
    trnx_type: string
    trnx_status: string
}





export interface TransactionFilters {
    company?: string;
    start_date?: string;
    end_date?: string;
    status?: string;
    page?: number;
    page_size?: number;
}

export const getTransactionDetails = async (filters?: TransactionFilters) => {
    let url = `/dashboard/transactions_details/`;

    if (filters) {
        const params = new URLSearchParams();

        if (filters.company) params.append('company', filters.company);
        if (filters.start_date) params.append('start_date', filters.start_date);
        if (filters.end_date) params.append('end_date', filters.end_date);
        if (filters.status) params.append('status', filters.status);
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.page_size) params.append('page_size', filters.page_size.toString());

        const queryString = params.toString();
        if (queryString) {
            url += `?${queryString}`;
        }
    }

    console.log('Transaction API URL:', url);
    const response = await adminAxios.get(url);
    return response.data as TransactionResponse;
};


export const useTransactionDetails = (filters?: TransactionFilters) => {
    return useQuery({
        queryKey: ["transaction-details", filters],
        queryFn: () => getTransactionDetails(filters),
        staleTime: 0,
        refetchOnWindowFocus: true,
        refetchOnMount: true
    })

}

