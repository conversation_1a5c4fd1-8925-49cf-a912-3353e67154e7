import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={188}
    height={111}
    viewBox="0 0 188 111"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={188} height={111} rx={8} fill="#D0EFF9" fillOpacity={0.5} />
    <rect
      x={44}
      y={20}
      width={100}
      height={71.6418}
      rx={8}
      fill="#B1E5F6"
      fillOpacity={0.5}
    />
    <g clipPath="url(#clip0_4643_1638)">
      <path
        d="M78.5 72H99.5C100.327 72 101 71.327 101 70.5V63C101 62.99 100.995 62.982 100.994 62.972C101.049 62.937 101.106 62.904 101.16 62.867L109.146 70.853C109.192 70.9008 109.247 70.939 109.308 70.9653C109.369 70.9916 109.435 71.0054 109.501 71.0061C109.567 71.0068 109.633 70.9942 109.695 70.9692C109.756 70.9441 109.812 70.9071 109.859 70.8602C109.906 70.8133 109.943 70.7575 109.969 70.6961C109.994 70.6347 110.007 70.5689 110.006 70.5025C110.006 70.4361 109.992 70.3705 109.966 70.3095C109.94 70.2484 109.902 70.1932 109.854 70.147L101.941 62.234C102.59 61.6282 103.108 60.8953 103.462 60.0809C103.817 59.2666 104 58.3881 104 57.5C103.999 56.4088 103.723 55.3354 103.198 54.3791C102.672 53.4227 101.914 52.6141 100.994 52.028C100.995 52.018 101 52.01 101 52V49.5C101 49.483 100.992 49.469 100.991 49.453C100.989 49.43 100.983 49.41 100.978 49.388C100.962 49.3185 100.932 49.2533 100.888 49.197C100.881 49.188 100.882 49.177 100.875 49.168L92.875 40.168C92.872 40.165 92.868 40.165 92.865 40.162C92.8041 40.0981 92.727 40.0518 92.642 40.028C92.623 40.022 92.606 40.02 92.586 40.017C92.557 40.012 92.53 40 92.5 40H78.5C77.673 40 77 40.673 77 41.5V70.5C77 71.327 77.673 72 78.5 72ZM103 57.5C103 60.533 100.532 63 97.5 63C94.468 63 92 60.533 92 57.5C92 54.467 94.468 52 97.5 52C100.532 52 103 54.467 103 57.5ZM93 41.815L99.387 49H93.5C93.28 49 93 48.58 93 48.25V41.815ZM78 41.5C78 41.3674 78.0527 41.2402 78.1464 41.1464C78.2402 41.0527 78.3674 41 78.5 41H92V48.25C92 49.059 92.655 50 93.5 50H100V51.501C98.7921 50.9957 97.4595 50.8685 96.1778 51.1361C94.8962 51.4038 93.7258 52.0536 92.821 53H82.5C82.3674 53 82.2402 53.0527 82.1464 53.1464C82.0527 53.2402 82 53.3674 82 53.5C82 53.6326 82.0527 53.7598 82.1464 53.8536C82.2402 53.9473 82.3674 54 82.5 54H92.032C91.4525 54.9008 91.1067 55.9319 91.026 57H82.5C82.3674 57 82.2402 57.0527 82.1464 57.1464C82.0527 57.2402 82 57.3674 82 57.5C82 57.6326 82.0527 57.7598 82.1464 57.8536C82.2402 57.9473 82.3674 58 82.5 58H91.025C91.1062 59.068 91.4519 60.099 92.031 61H82.5C82.3674 61 82.2402 61.0527 82.1464 61.1464C82.0527 61.2402 82 61.3674 82 61.5C82 61.6326 82.0527 61.7598 82.1464 61.8536C82.2402 61.9473 82.3674 62 82.5 62H92.75C92.771 62 92.789 61.991 92.81 61.988C93.7144 62.9384 94.8862 63.5916 96.1701 63.8613C97.4541 64.1309 98.7896 64.0042 100 63.498V70.5C100 70.6326 99.9473 70.7598 99.8536 70.8536C99.7598 70.9473 99.6326 71 99.5 71H78.5C78.22 71 78 70.78 78 70.5V41.5Z"
        fill="#475569"
        fillOpacity={0.9}
      />
      <path
        d="M82.5 50H88.5C88.6326 50 88.7598 49.9473 88.8536 49.8536C88.9473 49.7598 89 49.6326 89 49.5C89 49.3674 88.9473 49.2402 88.8536 49.1464C88.7598 49.0527 88.6326 49 88.5 49H82.5C82.3674 49 82.2402 49.0527 82.1464 49.1464C82.0527 49.2402 82 49.3674 82 49.5C82 49.6326 82.0527 49.7598 82.1464 49.8536C82.2402 49.9473 82.3674 50 82.5 50ZM82.5 66H95.5C95.6326 66 95.7598 65.9473 95.8536 65.8536C95.9473 65.7598 96 65.6326 96 65.5C96 65.3674 95.9473 65.2402 95.8536 65.1464C95.7598 65.0527 95.6326 65 95.5 65H82.5C82.3674 65 82.2402 65.0527 82.1464 65.1464C82.0527 65.2402 82 65.3674 82 65.5C82 65.6326 82.0527 65.7598 82.1464 65.8536C82.2402 65.9473 82.3674 66 82.5 66Z"
        fill="#475569"
        fillOpacity={0.9}
      />
    </g>
    <defs>
      <clipPath id="clip0_4643_1638">
        <rect
          width={33}
          height={32}
          fill="white"
          transform="translate(77 40)"
        />
      </clipPath>
    </defs>
  </svg>
);
export default SVGComponent;
