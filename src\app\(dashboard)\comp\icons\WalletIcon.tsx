import * as React from "react";
import { SVGProps } from "react";
const WalletIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={12}
    height={12}
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3.5849 11.375C2.3949 11.375 1.41992 10.51 1.41992 9.44501V8.42499C1.41992 8.21999 1.58992 8.04999 1.79492 8.04999C1.99992 8.04999 2.16992 8.21999 2.16992 8.42499C2.16992 9.04999 2.7749 9.51999 3.5849 9.51999C4.3949 9.51999 4.99991 9.04999 4.99991 8.42499C4.99991 8.21999 5.16991 8.04999 5.37491 8.04999C5.57991 8.04999 5.74991 8.21999 5.74991 8.42499V9.44501C5.74991 10.51 4.7799 11.375 3.5849 11.375ZM2.2999 9.935C2.5199 10.345 3.0149 10.625 3.5849 10.625C4.1549 10.625 4.6499 10.34 4.8699 9.935C4.5149 10.15 4.0749 10.275 3.5849 10.275C3.0949 10.275 2.6549 10.15 2.2999 9.935Z"
      fill="#099976"
    />
    <path
      d="M3.5849 8.89997C2.7649 8.89997 2.02992 8.52496 1.66492 7.92996C1.50492 7.66996 1.41992 7.36496 1.41992 7.05496C1.41992 6.52996 1.64992 6.03996 2.06992 5.67496C2.87992 4.96496 4.2749 4.96499 5.0899 5.66999C5.5099 6.03999 5.7449 6.52996 5.7449 7.05496C5.7449 7.36496 5.65991 7.66996 5.49991 7.92996C5.13991 8.52496 4.4049 8.89997 3.5849 8.89997ZM3.5849 5.87497C3.1949 5.87497 2.83491 6.00496 2.56491 6.23996C2.30991 6.45996 2.16992 6.74996 2.16992 7.05496C2.16992 7.22996 2.2149 7.38998 2.3049 7.53998C2.5349 7.91998 3.0249 8.15497 3.5849 8.15497C4.1449 8.15497 4.63489 7.91999 4.85989 7.54499C4.94989 7.39999 4.9949 7.23497 4.9949 7.05997C4.9949 6.75497 4.85491 6.46496 4.59991 6.23996C4.33491 6.00496 3.9749 5.87497 3.5849 5.87497Z"
      fill="#099976"
    />
    <path
      d="M3.5849 10.275C2.3499 10.275 1.41992 9.47999 1.41992 8.42999V7.05499C1.41992 5.98999 2.3899 5.125 3.5849 5.125C4.1499 5.125 4.68991 5.32001 5.09491 5.67001C5.51491 6.04001 5.74991 6.52999 5.74991 7.05499V8.42999C5.74991 9.47999 4.8199 10.275 3.5849 10.275ZM3.5849 5.875C2.8049 5.875 2.16992 6.40499 2.16992 7.05499V8.42999C2.16992 9.05499 2.7749 9.52499 3.5849 9.52499C4.3949 9.52499 4.99991 9.05499 4.99991 8.42999V7.05499C4.99991 6.74999 4.85992 6.45999 4.60492 6.23499C4.33492 6.00499 3.9749 5.875 3.5849 5.875Z"
      fill="#099976"
    />
    <path
      d="M9.51994 7.39996C8.76494 7.39996 8.12496 6.83996 8.06496 6.11996C8.02496 5.70496 8.17496 5.29997 8.47496 5.00497C8.72496 4.74497 9.07994 4.59998 9.45494 4.59998H10.5C10.995 4.61498 11.375 5.00495 11.375 5.48495V6.51498C11.375 6.99498 10.995 7.38496 10.515 7.39996H9.51994ZM10.4849 5.34998H9.45995C9.28495 5.34998 9.12496 5.41497 9.00996 5.53497C8.86496 5.67497 8.79496 5.86496 8.81496 6.05496C8.83996 6.38496 9.15994 6.64996 9.51994 6.64996H10.5C10.565 6.64996 10.625 6.58998 10.625 6.51498V5.48495C10.625 5.40995 10.5649 5.35498 10.4849 5.34998Z"
      fill="#099976"
    />
    <path
      d="M8.00012 10.625H6.75012C6.54512 10.625 6.37512 10.455 6.37512 10.25C6.37512 10.045 6.54512 9.875 6.75012 9.875H8.00012C9.29012 9.875 10.1251 9.04 10.1251 7.75V7.39999H9.52011C8.76511 7.39999 8.12512 6.84 8.06512 6.12C8.02512 5.705 8.17513 5.3 8.47513 5.005C8.72513 4.745 9.08011 4.60001 9.45511 4.60001H10.1201V4.25C10.1201 3.08 9.43513 2.27499 8.32513 2.14499C8.20513 2.12499 8.10012 2.125 7.99512 2.125H3.49512C3.37512 2.125 3.26011 2.13499 3.14511 2.14999C2.04511 2.28999 1.37012 3.09 1.37012 4.25V5.25C1.37012 5.455 1.20012 5.625 0.995117 5.625C0.790117 5.625 0.620117 5.455 0.620117 5.25V4.25C0.620117 2.71 1.57014 1.595 3.04514 1.41C3.18014 1.39 3.33512 1.375 3.49512 1.375H7.99512C8.11512 1.375 8.27011 1.38 8.43011 1.405C9.90512 1.575 10.8701 2.695 10.8701 4.25V4.97501C10.8701 5.18001 10.7001 5.35001 10.4951 5.35001H9.45511C9.28011 5.35001 9.12013 5.415 9.00513 5.535C8.86013 5.675 8.79012 5.86499 8.81012 6.05499C8.83512 6.38499 9.15514 6.64999 9.51514 6.64999H10.5001C10.7051 6.64999 10.8751 6.81999 10.8751 7.02499V7.75C10.8751 9.47 9.72012 10.625 8.00012 10.625Z"
      fill="#099976"
    />
  </svg>
);
export default WalletIcon;
