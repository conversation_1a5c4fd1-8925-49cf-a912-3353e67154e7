"use client";

import { <PERSON><PERSON>, DrawerMenu } from "@/components/core";
import { DrawerClose } from "@/components/core/Drawer";
import { cn } from "@/utils/classNames";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import * as React from "react";
import CaretDown from "./CaretDown";
import Logo from "@/app/(dashboard)/comp/icons/logo";
import HomeIcon from "@/app/(dashboard)/comp/icons/home";
import Notifications from "@/app/(dashboard)/comp/icons/notification";
import { getAuthenticatedUser, useUser } from "@/app/(auth)/(onboarding)/misc";
// import CloseIcon from "@/app/(main)/misc/icons/CLoseIcon";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@radix-ui/react-dropdown-menu";

import { useAuth } from "@/contexts/authentication";
import { useCompany } from "@/contexts/CompanyContext";
import { Liberty } from "@/icons/core";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { sub } from "date-fns";
import { useQueryClient } from "@tanstack/react-query";


export function DashboardHeader() {

  const { data: userData, isLoading: loadingUser } = useUser();
  const { selectedCompany, setSelectedCompany } = useCompany();
  // console.log(userData, "userData")
  const pathname = usePathname(); // Get the current pathname
  const router = useRouter();

  console.log(userData, "User data")
  console.log("Selected company:", selectedCompany)

  // Define the navigation link type
  type NavLink = {
    title: string;
    link: string;
    isChildren: boolean;
    subLinks: { title: string; onClick?: () => void; }[];
  };

  // Handle company selection
  const handleCompanySelect = (companyName: string) => {
    setSelectedCompany(companyName);
  };

  // Handle "All Companies" selection
  const handleAllCompaniesSelect = () => {
    setSelectedCompany(null);
  };

  // Generate dynamic navigation links based on API data
  const generateNavLinks = (): NavLink[] => {
    const baseLinks: NavLink[] = [
      {
        title: "Overview",
        link: "/dashboard",
        isChildren: false,
        subLinks: [],
      }
    ];

    // Add Companies dropdown if data is available
    if (userData?.data?.companies) {
      const validCompanies = userData.data.companies.filter(
        (company) => company.company_name && company.company_name.trim() !== ""
      );

      if (validCompanies.length > 0) {
        const companiesSubLinks = [
          {
            title: "All Companies",
            onClick: handleAllCompaniesSelect,
          },
          ...validCompanies.map((company) => ({
            title: company.company_name,
            onClick: () => handleCompanySelect(company.company_name),
          }))
        ];

        const companiesLink: NavLink = {
          title: selectedCompany ? selectedCompany : "Companies",
          link: "/dashboard",
          subLinks: companiesSubLinks,
          isChildren: true,
        };
        baseLinks.push(companiesLink);
      }
    }

    // Add Transactions link
    baseLinks.push({
      title: "Transactions",
      link: "/dashboard/transactions",
      isChildren: false,
      subLinks: [],
    });

    return baseLinks;
  };

  const navLinks = generateNavLinks();

  const { replace } = useRouter();
  const queryClient = useQueryClient();
  // const user = await getAuthenticatedUser();
  //     authDispatch({ type: "LOGIN", payload: user });
  const { authDispatch } = useAuth();
  const handleLogoutClick = () => {
    if (authDispatch) authDispatch({ type: "LOGOUT" });
    queryClient.clear();
    replace("/login");
  };


  return (
    <>
      <header className="bg-main px-6  md:px-[4.5rem]  py-6 md:py-[36.5px]">
        <div className="z-50 flex items-center justify-between">
          <a href={"/"} className="flex text-white items-center gap-3">
            {/* <Liberty className="max-lg:max-w-[100px]" /> */}

            <div className="md:text-2xl text-xl md:font-bold font-semibold px-[10px]">Core Banking</div>

            {/* <div className="font-wix-display">
              <h2 className="font-extrabold capitalize text-base md:text-xl text-nowrap leading-3">
                Liberty Life
              </h2>
              <p className="text-[.625rem] text-white text-opacity-65">
                by LibertyAssured.
              </p>
            </div> */}
          </a>

          <div className="hidden lg:block ">
            <nav>
              <ul className="flex items-center gap-[2.875rem] ">
                {navLinks?.map((link, idx: number) => (
                  <>
                    {link?.isChildren ? (
                      <div className="z-50 text-white text-sm text-opacity-65 hover:text-white relative group" >
                        <Popover>
                          <PopoverTrigger>
                            <Button className=" text-white text-opacity-65 hover:text-white">
                              {link.title}
                              <svg
                                width="10"
                                height="7"
                                viewBox="0 0 10 7"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                className="ml-3"
                              >
                                <path
                                  d="M8.825 0.158333L5 3.975L1.175 0.158333L0 1.33333L5 6.33333L10 1.33333L8.825 0.158333Z"
                                  fill="white"
                                />
                              </svg>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="flex flex-col gap-1 px-1 py-2 text-xs md:w-40 md:h-[300px] overflow-y-auto bg-[#0B1739]  rounded-xl text-white border border-[#0B1739] shadow-lg">
                            {link?.subLinks?.map((subLink, idx: number) => {
                              const isSelected = selectedCompany === subLink.title ||
                                (!selectedCompany && subLink.title === "All Companies");

                              return (
                                <button
                                  key={idx}
                                  onClick={subLink.onClick}
                                  className={cn(
                                    "text-white text-xs font-normal text-opacity-65 hover:text-main-bg hover:bg-[#132863] rounded-10 px-3 py-2 text-left transition-colors duration-200",
                                    isSelected && "bg-[#132863] text-main-bg font-semibold"
                                  )}
                                >
                                  {subLink.title}
                                  {isSelected && (
                                    <span className="ml-2 text-green-400">✓</span>
                                  )}
                                </button>
                              );
                            })}
                          </PopoverContent>
                        </Popover>
                      </div>
                    ) : (
                      <>
                        <Link
                          href={link?.link}
                          className={cn(
                            "text-white text-sm text-opacity-65 hover:text-main-bg ",
                            pathname === link.link && "text-main-bg font-bold "
                          )}
                          key={idx}
                          title={link?.title}
                        >
                          {link?.title}
                        </Link>
                      </>
                    )}
                  </>
                ))}
              </ul>
            </nav>
          </div>

          <div className="flex items-center md:gap-x-3 gap-x-2 ">
            <div className="flex items-center gap-x-3 lg:hidden">
              <DrawerMenu
                trigger={
                  <Button
                    className={cn(
                      "lg:hidden bg-white/10 px-5 py-2.5 rounded-full",
                      "font-display"
                    )}
                  >
                    Menu
                  </Button>
                }
                contentClass="bg-main border-main"
              >
                <div className="text-white p-5 pb-0 gap-5">
                  <header className="flex items-center justify-between">
                    <h6 className="font-semibold text-lg">Menu Content</h6>
                    <DrawerClose
                      className={cn(
                        "bg-white/10 h-8 w-8 flex justify-center items-center  rounded-full text-white/50 rotate-12 text-lg hover:text-white",
                        "font-display"
                      )}
                    >
                      {/* <CloseIcon color="#fff"/> */}
                    </DrawerClose>
                  </header>

                  <ul
                    className={cn(
                      "font-display",
                      "flex flex-col gap-8 font-normal mt-10"
                    )}
                  >
                    {navLinks?.map((link, idx: number) => (


                      <li
                        className="border-b-[.0094rem] border-b-white/30 p-2"
                        key={idx}
                      >

                        <a
                          href={link?.link}
                          className={cn(
                            "text-white text-sm text-opacity-65 hover:text-main-bg",
                            pathname === link.link && "text-main-bg font-bold"
                          )}
                          title={link?.title}
                        >
                          {link?.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </DrawerMenu>
            </div>

            <div className="flex">
              <div className="flex gap-4">
                {/* <Link href="/">
                <Button className="rounded-full w-9 h-9 hidden lg:flex justify-center items-center shrink-0 bg-[#FFFFFF4D] p-2">
                  <HomeIcon height={15} width={16} />
                </Button>
              </Link> */}
                <Button className="rounded-full w-9 h-9 hidden lg:flex justify-center items-center shrink-0 bg-[#FFFFFF4D] p-2">
                  <Notifications height={20} width={20} />
                </Button>
                {!loadingUser && (
                  <button className="rounded-full w-9 h-9 text-sm text-white flex justify-center items-center shrink-0 bg-[#FFFFFF4D] ">
                    {`LI`}

                    {/* <img
                      src="/images/Ellipse.png"
                      style={{ width: "100%", height: "100%" }}
                      alt="user"
                      className="size-fit"
                    /> */}
                  </button>
                  //               <Button className="rounded-full w-9 h-9 text-sm text-white flex justify-center items-center shrink-0 bg-[#FFFFFF4D] p-1">
                  //                                   <Avatar>
                  //   <AvatarImage src="/images/Ellipse.png" alt="user" />
                  //   <AvatarFallback>CN</AvatarFallback>
                  // </Avatar>
                  // </Button>
                )}

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button className="rounded-full -ml-3 w-9 h-9 text-sm text-white flex justify-center items-center shrink-0 bg-transparent p-2">
                      <CaretDown />
                    </Button>
                  </DropdownMenuTrigger>

                  <DropdownMenuContent className="bg-white z-[999] mt-2 rounded-md shadow-md p-2">
                    <DropdownMenuItem
                      className="p-2 rounded-md text-sm text-gray-800 hover:bg-gray-100 cursor-pointer"
                      onClick={() => router.push("/dashboard/my-profile")}
                    >
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="my-1 h-px bg-gray-200" />
                    <DropdownMenuItem
                      onClick={handleLogoutClick}
                      className="p-2 rounded-md text-sm text-gray-800 hover:bg-gray-100 cursor-pointer"
                    >
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}
