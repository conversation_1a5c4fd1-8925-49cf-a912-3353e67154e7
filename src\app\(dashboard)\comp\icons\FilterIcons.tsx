import * as React from "react";
import { SVGProps } from "react";
const FiltersIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14.6666 4.33334H10.6666"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.00004 4.33334H1.33337"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.66671 6.66667C7.95537 6.66667 9.00004 5.622 9.00004 4.33333C9.00004 3.04467 7.95537 2 6.66671 2C5.37804 2 4.33337 3.04467 4.33337 4.33333C4.33337 5.622 5.37804 6.66667 6.66671 6.66667Z"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.6667 11.6667H12"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.33337 11.6667H1.33337"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.33333 14C10.622 14 11.6667 12.9553 11.6667 11.6667C11.6667 10.378 10.622 9.33334 9.33333 9.33334C8.04467 9.33334 7 10.378 7 11.6667C7 12.9553 8.04467 14 9.33333 14Z"
      stroke="#56566A"
      strokeMiterlimit={10}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default FiltersIcon;
