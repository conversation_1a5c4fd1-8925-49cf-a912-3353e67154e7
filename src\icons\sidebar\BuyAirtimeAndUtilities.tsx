'use client';


interface BuyAirtimeAndUtilitiesProps {
  isSelected?: boolean;
}

export function BuyAirtimeAndUtilities({
  isSelected,
}: BuyAirtimeAndUtilitiesProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.825 9.9c-.45 0-.908-.084-1.267-.242L3.642 7.475c-1.25-.559-1.434-1.309-1.434-1.717 0-.408.184-1.158 1.434-1.717l4.916-2.183c.725-.325 1.817-.325 2.542 0l4.925 2.183c1.242.55 1.433 1.309 1.433 1.717 0 .408-.183 1.158-1.433 1.717L11.1 9.658c-.367.167-.817.242-1.275.242Zm0-7.034c-.283 0-.558.042-.758.134L4.15 5.183c-.508.233-.692.467-.692.575 0 .108.184.35.684.575l4.916 2.183c.4.175 1.125.175 1.525 0l4.925-2.183c.509-.225.692-.467.692-.575 0-.108-.183-.35-.692-.575L10.592 3a2.104 2.104 0 0 0-.767-.134Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 14.242c-.317 0-.633-.067-.933-.2l-5.659-2.517c-.858-.375-1.533-1.416-1.533-2.358a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625c0 .458.375 1.033.792 1.225l5.658 2.517c.267.116.575.116.85 0l5.658-2.517c.417-.183.792-.767.792-1.225a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625c0 .942-.675 1.983-1.533 2.367l-5.659 2.516c-.3.125-.616.192-.933.192Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 18.409c-.317 0-.633-.067-.933-.2l-5.659-2.517a2.588 2.588 0 0 1-1.533-2.367.63.63 0 0 1 .625-.625c.342 0 .625.292.625.634 0 .524.308 1.008.792 1.225l5.658 2.516c.267.117.575.117.85 0l5.658-2.516c.484-.217.792-.692.792-1.225a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625c0 1.024-.6 1.95-1.533 2.366l-5.659 2.517c-.3.125-.616.192-.933.192Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
