"use client"

import { useState, useEffect } from "react"
import { Search, ChevronLeft, ChevronRight, CalendarIcon, X } from "lucide-react"
import { format } from "date-fns"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"


import { Checkbox } from "@/components/ui/checkbox"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

import { useTransactionDetails, type TransactionFilters } from "../../misc/getTransactionDetails"
import { useCompany } from "@/contexts/CompanyContext"
import { Button, Input } from "@/components/core"
import { Calendar } from "@/components/ui/calendar"

// Sample data for the transactions
const transactions = [
  {
    id: 1,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DDD",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 2,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DDG",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 3,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DGG",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Failed",
  },
  {
    id: 4,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039403DIG",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 5,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DIO",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Pending",
  },
  {
    id: 6,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DIG",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 7,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DIG",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 8,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DIO",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 9,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039403DIO",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
  {
    id: 10,
    date: "12 June, 2024 4:44pm",
    reference: "NF2039402DIO",
    balanceBefore: "₦10,000",
    amount: "₦10,000",
    balanceAfter: "₦10,000",
    type: "Credit",
    status: "Successful",
  },
]

// Sample data for the chart
const chartData = [
  { name: "January", inflow: 25, outflow: 15 },
  { name: "February", inflow: 30, outflow: 20 },
  { name: "March", inflow: 35, outflow: 25 },
  { name: "April", inflow: 40, outflow: 30 },
  { name: "May", inflow: 30, outflow: 20 },
  { name: "June", inflow: 25, outflow: 15 },
  { name: "July", inflow: 35, outflow: 25 },
  { name: "August", inflow: 40, outflow: 30 },
  { name: "September", inflow: 45, outflow: 35 },
  { name: "October", inflow: 50, outflow: 40 },
  { name: "November", inflow: 45, outflow: 35 },
  { name: "December", inflow: 55, outflow: 45 },
]

export default function FinancialDashboard() {
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedTransactions, setSelectedTransactions] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [showFilters, setShowFilters] = useState(false)

  // Date state for calendar
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  // Filter states
  const [filters, setFilters] = useState<TransactionFilters>({
    page: 1,
    page_size: 10,
  })

  // Get selected company from context
  const { selectedCompany } = useCompany()

  // Build final filters including selected company
  const finalFilters: TransactionFilters = {
    ...filters,
    company: selectedCompany || undefined,
  }

  const { data: transactionDetails, isLoading } = useTransactionDetails(finalFilters)

  console.log(transactionDetails, "Transaction details with filters:", finalFilters)

  // Get actual transactions from API or fallback to sample data
  const actualTransactions = transactionDetails?.data?.transaction?.results || transactions

  // Filter handlers
  const handleFilterChange = (key: keyof TransactionFilters, value: string | number) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  // Update filters when date changes
  useEffect(() => {
    if (startDate) {
      handleFilterChange("start_date", format(startDate, "yyyy-MM-dd"))
    }
    if (endDate) {
      handleFilterChange("end_date", format(endDate, "yyyy-MM-dd"))
    }
  }, [startDate, endDate])

  const clearFilters = () => {
    setFilters({
      page: 1,
      page_size: 10,
    })
    setStartDate(undefined)
    setEndDate(undefined)
  }

  const toggleSelectAll = () => {
    if (selectedTransactions.length === actualTransactions.length) {
      setSelectedTransactions([])
    } else {
      setSelectedTransactions(
        actualTransactions.map((t, index) => ("trnx_id" in t ? Number.parseInt(t.trnx_id) : t.id || index)),
      )
    }
  }

  const toggleSelect = (id: number) => {
    if (selectedTransactions.includes(id)) {
      setSelectedTransactions(selectedTransactions.filter((t) => t !== id))
    } else {
      setSelectedTransactions([...selectedTransactions, id])
    }
  }

  // Fixed status badge function
  const getStatusBadgeClass = (status: string) => {
    const statusLower = typeof status === "string" ? status.toLowerCase() : ""

    if (statusLower.includes("success") || statusLower === "successful") {
      return "bg-emerald-500/20 text-emerald-500 border border-emerald-500/30"
    } else if (statusLower.includes("fail") || statusLower === "failed") {
      return "bg-red-500/20 text-red-500 border border-red-500/30"
    } else if (statusLower.includes("pend") || statusLower === "pending") {
      return "bg-amber-500/20 text-amber-500 border border-amber-500/30"
    } else {
      return "bg-gray-500/20 text-gray-500 border border-gray-500/30"
    }
  }

  return (
    <div className="min-h-screen text-white">
      <div className="w-full mx-auto space-y-8">
        {/* Transactions Table */}
        <div className="bg-[#0B1739] border border-blue-800/30 rounded-lg p-6 overflow-hidden">
          <div className="py-4 flex justify-between items-center flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-medium">Recent Transactions</h2>
              <div className="bg-[#122251] text-white border border-gray-600 px-2 py-1 rounded text-xs font-medium">
                {actualTransactions.length}
              </div>
              <div className="relative h-[44px] border-[0.4px] border-[#667085] rounded-lg">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#696969] h-4 w-4" />
                <Input
                  placeholder="Search a transaction"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 bg-inherit text-[#696969] focus:border-blue-600 h-full"
                />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setShowFilters(!showFilters)}
                variant="outline"
                className="bg-[#122251] border-[#667085] rounded-lg text-blue-100 hover:bg-blue-800/20 w-[104px] h-[44px] gap-2 font-medium text-sm"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M14.6667 4.33331H10.6667"
                    stroke="white"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M4.00001 4.33331H1.33334"
                    stroke="white"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M6.66668 6.66667C7.95534 6.66667 9.00001 5.622 9.00001 4.33333C9.00001 3.04467 7.95534 2 6.66668 2C5.37801 2 4.33334 3.04467 4.33334 4.33333C4.33334 5.622 5.37801 6.66667 6.66668 6.66667Z"
                    stroke="white"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M14.6667 11.6667H12"
                    stroke="white"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M5.33334 11.6667H1.33334"
                    stroke="white"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M9.33333 14C10.622 14 11.6667 12.9553 11.6667 11.6666C11.6667 10.378 10.622 9.33331 9.33333 9.33331C8.04467 9.33331 7 10.378 7 11.6666C7 12.9553 8.04467 14 9.33333 14Z"
                    stroke="white"
                    strokeMiterlimit="10"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Filter
              </Button>
            </div>
          </div>

          {/* Redesigned Filter Panel */}
          {showFilters && (
            <div className="bg-[#122251] mb-6 border border-blue-700/50 shadow-lg rounded-lg">
              <div className="p-5">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-white font-medium">Filter Transactions</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                    onClick={() => setShowFilters(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                  {/* Status Filter */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">Status</label>
                    <Select value={filters.status || ""} onValueChange={(value) => handleFilterChange("status", value)}>
                      <SelectTrigger className="bg-[#0B1739] border-blue-700/50 text-white">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#0B1739] border-blue-700/50 text-white">
                        <SelectItem value="ALL">All Statuses</SelectItem>
                        <SelectItem value="SUCCESSFUL">Successful</SelectItem>
                        <SelectItem value="FAILED">Failed</SelectItem>
                        <SelectItem value="PENDING">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Start Date Picker */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">Start Date</label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={`w-full justify-start text-left font-normal bg-[#0B1739] border-blue-700/50 ${!startDate && "text-gray-400"}`}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, "PPP") : "Select date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-[#0B1739] border-blue-700/50">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                          className="bg-[#0B1739] text-white"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* End Date Picker */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">End Date</label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={`w-full justify-start text-left font-normal bg-[#0B1739] border-blue-700/50 ${!endDate && "text-gray-400"}`}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, "PPP") : "Select date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0 bg-[#0B1739] border-blue-700/50">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                          className="bg-[#0B1739] text-white"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Active Filters and Actions */}
                <div className="mt-5 flex flex-wrap items-center justify-between gap-4">
                  <div className="flex flex-wrap gap-2">
                    {filters.status && (
                      <div className="bg-blue-900/50 text-blue-200 px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                        Status: {filters.status}
                        <button
                          className="h-4 w-4 p-0 text-blue-200 hover:text-white ml-1 flex items-center justify-center"
                          onClick={() => handleFilterChange("status", "")}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    )}
                    {startDate && (
                      <div className="bg-blue-900/50 text-blue-200 px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                        From: {format(startDate, "MMM d, yyyy")}
                        <button
                          className="h-4 w-4 p-0 text-blue-200 hover:text-white ml-1 flex items-center justify-center"
                          onClick={() => setStartDate(undefined)}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    )}
                    {endDate && (
                      <div className="bg-blue-900/50 text-blue-200 px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                        To: {format(endDate, "MMM d, yyyy")}
                        <button
                          className="h-4 w-4 p-0 text-blue-200 hover:text-white ml-1 flex items-center justify-center"
                          onClick={() => setEndDate(undefined)}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearFilters}
                      className="bg-transparent border-red-500/50 text-red-400 hover:bg-red-900/20 hover:text-red-300"
                    >
                      Clear All
                    </Button>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                      Apply Filters
                    </Button>
                  </div>
                </div>

                {selectedCompany && (
                  <div className="mt-3 p-2 bg-blue-900/30 rounded border border-blue-600/30">
                    <span className="text-sm text-blue-200 flex items-center gap-2">
                      <span className="bg-blue-500/20 p-1 rounded">
                        <CalendarIcon className="h-3 w-3 text-blue-300" />
                      </span>
                      Filtering transactions for: <strong>{selectedCompany}</strong>
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-[#122251]">
                <tr>
                  <th className="px-4 pl-10 py-3 text-left">
                    <Checkbox
                      checked={selectedTransactions.length === transactions.length}
                      onCheckedChange={toggleSelectAll}
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Date/Time</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Reference ID</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Balance before</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Amount</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Balance after</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Type</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-blue-800/50">
                {isLoading
                  ? // Loading skeleton
                    [...Array(5)].map((_, index) => (
                      <tr key={index} className="hover:bg-blue-800/20">
                        <td className="px-4 pl-10 py-3">
                          <div className="h-4 w-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="h-4 bg-gray-600 rounded animate-pulse"></div>
                        </td>
                      </tr>
                    ))
                  : actualTransactions.map((transaction, index) => {
                      const transactionId =
                        "trnx_id" in transaction ? Number.parseInt(transaction.trnx_id) : transaction.id || index
                      const isApiTransaction = "trnx_id" in transaction
                      const status = isApiTransaction ? transaction.trnx_status : transaction.status

                      return (
                        <tr key={transactionId} className="hover:bg-blue-800/20">
                          <td className="px-4 pl-10 py-3">
                            <Checkbox
                              checked={selectedTransactions.includes(transactionId)}
                              onCheckedChange={() => toggleSelect(transactionId)}
                            />
                          </td>
                          <td className="px-4 py-3 text-sm opacity-85">
                            {isApiTransaction ? transaction.trnx_date : transaction.date}
                          </td>
                          <td className="px-4 py-3 text-sm opacity-85">
                            {isApiTransaction ? transaction.reference : transaction.reference}
                          </td>
                          <td className="px-4 py-3 text-sm opacity-85">
                            {isApiTransaction
                              ? `₦${transaction.balance_before?.toLocaleString()}`
                              : transaction.balanceBefore}
                          </td>
                          <td className="px-4 py-3 text-sm opacity-85">
                            {isApiTransaction ? `₦${transaction.amount?.toLocaleString()}` : transaction.amount}
                          </td>
                          <td className="px-4 py-3 text-sm opacity-85">
                            {isApiTransaction
                              ? `₦${transaction.balance_after?.toLocaleString()}`
                              : transaction.balanceAfter}
                          </td>
                          <td className="px-4 py-3 text-sm opacity-85">
                            {isApiTransaction ? transaction.trnx_type : transaction.type}
                          </td>
                          <td className="px-4 py-3">
                            <span className={`px-3 py-1 rounded-lg text-xs font-medium ${getStatusBadgeClass(status)}`}>
                              {status}
                            </span>
                          </td>
                        </tr>
                      )
                    })}
              </tbody>
            </table>
          </div>

          <div className="pl-[20px] md:pl-[85px] py-6 flex justify-between items-center border-t border-blue-800">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon" className="h-8 w-8 bg-[#122251] border-[0.3px] border-[#667085]">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 bg-[#122251] border-[0.3px] border-[#667085]">
                1
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 bg-[#122251] border-[0.3px] border-[#667085]">
                ...
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 bg-[#122251] border-[0.3px] border-[#667085]">
                15
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8 bg-[#122251] border-[0.3px] border-[#667085]">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Chart Section */}
        <div className="bg-[#0B1739] rounded-lg shadow-xl p-6">
          <div className="flex justify-between items-center gap-2 md:gap-8 mb-6 flex-wrap">
            <h2 className="text-lg font-medium">Transactions</h2>
            <div className="flex items-center gap-2 lg:gap-10">
              <div className="flex items-center gap-2">
                <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-emerald-400"></span>
                <span className="text-xs md:text-sm">Total Inflow - ₦112,190,000</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-blue-400"></span>
                <span className="text-xs md:text-sm">Total Outflow - ₦112,190,000</span>
              </div>
            </div>

            <Select defaultValue="jan-dec-2024">
              <SelectTrigger className="lg:w-52 bg-blue-950/50 border-blue-700 text-xs md:text-sm">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent className="whitespace-nowrap bg-[#0B1739] border-blue-700 text-white">
                <SelectItem value="jan-dec-2024" className="whitespace-nowrap">
                  Jan 2024 - Jun 2024
                </SelectItem>
                <SelectItem value="jul-dec-2024" className="whitespace-nowrap">
                  Jul 2024 - Dec 2024
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="h-80 bg-[#122251] rounded-lg p-4">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 30, right: 30, left: 20, bottom: 10 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#1e3a8a" vertical={false} horizontal={true} />
                <XAxis dataKey="name" axisLine={false} tickLine={false} tick={{ fill: "#94a3b8", fontSize: 12 }} />
                <YAxis
                  axisLine={false}
                  tickLine={true}
                  tick={{ fill: "#94a3b8", fontSize: 12 }}
                  tickFormatter={(value) => `${value}M`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#0f172a",
                    borderColor: "#1e40af",
                    borderRadius: "0.375rem",
                    color: "#f8fafc",
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="inflow"
                  stroke="#10b981"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: "#10b981" }}
                />
                <Line
                  type="monotone"
                  dataKey="outflow"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: "#3b82f6" }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  )
}
