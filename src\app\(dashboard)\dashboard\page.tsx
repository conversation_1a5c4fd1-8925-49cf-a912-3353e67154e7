"use client";
import { <PERSON><PERSON> } from "@/components/core";
import Image from "next/image";
import React, { useEffect, useState } from "react";

import TransactionsTable from "../comp/components/transactions/table/TransactionsTable";
import { useUser } from "@/app/(auth)/(onboarding)/misc";
import DashboardPlanHeader from "../comp/components/DashboardPlanHeader";
import { DasboardOverview } from "../comp/components/DashboardOverview";

const Dashboard = () => {
const { data: userData, isLoading } = useUser();

  return (
    <div className="relative bg-main w-full">
      {/* <DashboardPlanHeader /> */}

      <DasboardOverview />

      <div className="w-full mt-4 rounded-10">
        <TransactionsTable />
      </div>

      <div className="relative p-2"></div>
    </div>
  );
};

export default Dashboard;
