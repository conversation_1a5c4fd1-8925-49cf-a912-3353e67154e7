'use client'
import { D<PERSON>_Sans, Wix_Madefor_Display } from "next/font/google";
import "./globals.css";
import { cn } from "@/utils/classNames";
import { Suspense, useState } from "react";
import {ReactQueryProvider} from "@/lib/reactQuery";
import { AuthProvider } from "@/contexts/authentication";
import ProtectedRouteGuard from "./(auth)/(onboarding)/misc/components/ProtectedRouteGuard";
import { Wrapper } from "./(auth)/(onboarding)/misc/components/Wrapper";
// import { useUser } from "./(auth)/(onboarding)/misc";
import { Button } from "@/components/core";
import Image from "next/image";
import { Toaster } from "react-hot-toast";
import Script from "next/script";


// import NemLogoIcon from "@/components/icons/NemIcon";
// import Head from "next/head";
// import NemLogoIcon from "@/components/icons/NemIcon";

const sans = DM_Sans({
  subsets: ["latin"],
  variable: "--font-sans",
  display: "swap",
});
const display = Wix_Madefor_Display({
  subsets: ["latin"],
  variable: "--font-display",
  display: "swap",
});

// export const metadata: Metadata = {
//   title: "Create Next App",
//   description: "Generated by create next app",
// };

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
 
  const url = `https://wa.link/vrg8zn`;

  const [showCheckPlanModal, setShowCheckPlanModal] = useState(false)
  return (
    <html className={cn(sans.variable, display.variable)} lang="en">
      
      <head>
     
        <Script id="google-tag-manager" strategy="afterInteractive">
          {`
           window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'AW-16669350340')
      `}
        </Script>
        <Script
          dangerouslySetInnerHTML={{
            __html: `src='hhttps://www.googletagmanager.com/gtag/js?id=AW-16669350340`,
          }}
          id="liberty-life-widget-icon-freshworks"
        />

          {/* Hotjar Tracking Code */}
          <Script id="hotjar-tracking" strategy="afterInteractive">
                {`
                    (function(h,o,t,j,a,r){
                        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                        h._hjSettings={hjid:5195904,hjsv:6};
                        a=o.getElementsByTagName('head')[0];
                        r=o.createElement('script');r.async=1;
                        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                        a.appendChild(r);
                    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
                `}
            </Script>

            <Script
          dangerouslySetInnerHTML={{
            __html: `!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '1565590854136448');
fbq('track', 'PageView');`,
          }}
          id="show-facebook-pixel"
        />

           
           <link rel="shortcut icon" href="/icon.ico" />
      
      </head>
      <body className="!z-[9999999999999999999999999999999999999999]">
        <Toaster
          containerStyle={{
            zIndex: 99999,
          }}
          position="top-center"
          toastOptions={{
            style: {
              zIndex: 999999,
            },
          }}
        />
        <ReactQueryProvider>
          <AuthProvider>
            <ProtectedRouteGuard>
              <Suspense fallback={<></>}>
                <Wrapper >{children}</Wrapper>
              </Suspense>
            </ProtectedRouteGuard>
          </AuthProvider>
          <div className="fixed left-1 right-2 flex md:justify-end items-end flex-col xl:right-0 bottom-[2.3rem] md:bottom-20">
  {/* <!-- Whatsapp Button with Bounce Animation --> */}
</div>
        </ReactQueryProvider>

      <noscript
          dangerouslySetInnerHTML={{
            __html: `<img height="1" width="1" style="display:none"src="https://www.facebook.com/tr?id=1565590854136448&ev=PageView&noscript=1"/>`,
          }}
        />
      </body>

      <footer className="relative">
    

   
    
      </footer>




      {/* Heala Configuration */}

    </html>
  );
}
