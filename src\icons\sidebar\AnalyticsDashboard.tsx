'use client';

interface AnalyticsDashboardProps {
  isSelected?: boolean;
}

export function AnalyticsDashboard({ isSelected }: AnalyticsDashboardProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.833 8.959h-1.666c-2.017 0-3.125-1.109-3.125-3.125V4.167c0-2.017 1.108-3.125 3.125-3.125h1.666c2.017 0 3.125 1.108 3.125 3.125v1.667c0 2.016-1.108 3.125-3.125 3.125Zm-1.666-6.667c-1.317 0-1.875.558-1.875 1.875v1.667c0 1.316.558 1.875 1.875 1.875h1.666c1.317 0 1.875-.559 1.875-1.875V4.167c0-1.317-.558-1.875-1.875-1.875h-1.666ZM5.833 18.959H4.167c-2.017 0-3.125-1.109-3.125-3.125v-1.667c0-2.017 1.108-3.125 3.125-3.125h1.666c2.017 0 3.125 1.108 3.125 3.125v1.667c0 2.016-1.108 3.125-3.125 3.125Zm-1.666-6.667c-1.317 0-1.875.558-1.875 1.875v1.667c0 1.316.558 1.875 1.875 1.875h1.666c1.317 0 1.875-.559 1.875-1.875v-1.667c0-1.317-.558-1.875-1.875-1.875H4.167ZM5 8.959A3.962 3.962 0 0 1 1.042 5 3.962 3.962 0 0 1 5 1.042 3.962 3.962 0 0 1 8.958 5 3.962 3.962 0 0 1 5 8.96Zm0-6.667A2.714 2.714 0 0 0 2.292 5 2.714 2.714 0 0 0 5 7.71 2.714 2.714 0 0 0 7.708 5 2.714 2.714 0 0 0 5 2.292Zm10 16.667A3.962 3.962 0 0 1 11.042 15 3.962 3.962 0 0 1 15 11.042 3.962 3.962 0 0 1 18.958 15 3.962 3.962 0 0 1 15 18.96Zm0-6.667A2.714 2.714 0 0 0 12.292 15 2.714 2.714 0 0 0 15 17.71 2.714 2.714 0 0 0 17.708 15 2.714 2.714 0 0 0 15 12.292Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
