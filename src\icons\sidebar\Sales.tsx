'use client';

interface SalesProps {
  isSelected?: boolean;
}

export function Sales({ isSelected }: SalesProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.733 15.75a.63.63 0 0 1-.625-.625V13.4a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v1.725c0 .35-.283.625-.625.625Zm4.267 0a.63.63 0 0 1-.625-.625v-3.458a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.458c0 .35-.283.625-.625.625Zm4.267 0a.63.63 0 0 1-.625-.625V9.94a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v5.184c0 .35-.275.625-.625.625Zm-8.534-4.767a.63.63 0 0 1-.608-.475.623.623 0 0 1 .458-.758 15.103 15.103 0 0 0 7.825-4.833l.384-.45a.63.63 0 0 1 .883-.067.63.63 0 0 1 .067.883l-.384.45a16.261 16.261 0 0 1-8.475 5.234.46.46 0 0 1-.15.016Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M14.267 7.933a.63.63 0 0 1-.625-.625V5.5h-1.817a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.442a.63.63 0 0 1 .625.625v2.442a.617.617 0 0 1-.625.616Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M12.5 18.959h-5c-4.525 0-6.458-1.934-6.458-6.459v-5c0-4.525 1.933-6.458 6.458-6.458h5c4.525 0 6.458 1.933 6.458 6.458v5c0 4.525-1.933 6.459-6.458 6.459Zm-5-16.667c-3.842 0-5.208 1.367-5.208 5.208v5c0 3.842 1.366 5.209 5.208 5.209h5c3.842 0 5.208-1.367 5.208-5.209v-5c0-3.841-1.366-5.208-5.208-5.208h-5Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
