import * as React from "react";
import { SVGProps } from "react";
const NoVisibleSVG = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_11302_2845)">
      <path
        d="M3.62999 3.624C4.62099 2.98 5.77099 2.5 6.99999 2.5C9.78999 2.5 12.18 4.975 13.23 6.246C13.396 6.453 13.488 6.722 13.488 7C13.488 7.279 13.396 7.547 13.23 7.754C12.651 8.454 11.665 9.521 10.43 10.337M8.49999 11.27C8.01799 11.416 7.51599 11.5 6.99999 11.5C4.20999 11.5 1.81999 9.025 0.769994 7.754C0.600613 7.5394 0.509591 7.27339 0.511994 7C0.511994 6.722 0.603994 6.453 0.769994 6.246C1.10299 5.844 1.56999 5.32 2.14199 4.792"
        stroke="white"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.414 8.414C8.77832 8.0368 8.97991 7.53159 8.97535 7.0072C8.97079 6.4828 8.76045 5.98118 8.38964 5.61036C8.01882 5.23955 7.5172 5.02921 6.9928 5.02465C6.46841 5.0201 5.9632 5.22168 5.586 5.586M13.5 13.5L0.5 0.5"
        stroke="white"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11302_2845">
        <rect width={14} height={14} fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export default NoVisibleSVG;
