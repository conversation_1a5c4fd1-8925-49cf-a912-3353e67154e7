"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/core";
import CopyIcon3 from "../icons/CopyIcon3";
import { SmallSpinner } from "@/icons/core";
import { capitalizeFirstLetter } from "@/utils";
import { UserDataTypes, useUser } from "@/app/(auth)/(onboarding)/misc";
import { useClipboard } from "@/hooks";
import { useQuery, useQueryClient } from "react-query";
import { fetchReferralCode } from "../../dashboard/api/referral/fetchReferralCode";
// import { getPlan } from "@/app/(main)/misc/components/insurance/api/plan/getPlan";

import { getUserCurrentPlan } from "../../dashboard/api/getCurrentPlan";



interface Prop {
    userData: UserDataTypes | undefined;
    loadinUser: boolean;
}



const ViewBeneficiaryHeader = ({ loadinUser, userData: users }: Prop) => {
    const { data: currentPlan, isLoading: loadingPlan } = useQuery({
        queryFn: () => getUserCurrentPlan(String(users?.phone_number)),
        queryKey: ["fetch-user-current-plan", users?.phone_number],
        enabled: !!users?.phone_number,
    });


    const { data: userData, isLoading } = useUser();

    const { copy } = useClipboard();
    const queryClient = useQueryClient();
    const [showMakePaymentModal, setshowMakePaymentModal] = useState(false);
    const [showPaymentModal, setShowPaymentModal] = useState(false);
    const [paymentData, setPaymentData] = useState({
        account_name: "",
        account_no: "",
        amount: "",
        bank_name: "",
        paystack_link: "",
    });
    const {
        data,
        refetch,

        isLoading: loadinGenerate,
    } = useQuery({
        queryFn: () => fetchReferralCode(userData?.id as string),
        queryKey: ["generate-referral-code", userData?.id],
        enabled: false,
        onSuccess: () => {
            // Invalidate user details query to refetch data.
            queryClient.invalidateQueries(["user-details", data?.referral_code]);
        },
    });

    return (
        <div className=" bg-main py-6 px-6  md:px-[4.5rem] lg:px-[7.5rem]  ">

        </div>
    );
};

export default ViewBeneficiaryHeader;
