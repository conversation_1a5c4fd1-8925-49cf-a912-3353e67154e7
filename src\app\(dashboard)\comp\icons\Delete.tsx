import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={14}
    height={14}
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M11.375 3.20866L11.0133 9.05658C10.9212 10.5505 10.8751 11.2977 10.5 11.835C10.3148 12.1005 10.0764 12.3246 9.8 12.493C9.24175 12.8337 8.49333 12.8337 6.9965 12.8337C5.49733 12.8337 4.74775 12.8337 4.18833 12.4924C3.91177 12.3237 3.67337 12.0992 3.48833 11.8332C3.11383 11.2954 3.06833 10.547 2.9785 9.05074L2.625 3.20866M1.75 3.20866H12.25M9.366 3.20866L8.96758 2.38733C8.70333 1.84133 8.57092 1.56891 8.34283 1.39858C8.29217 1.36084 8.23852 1.32729 8.18242 1.29824C7.92983 1.16699 7.6265 1.16699 7.02042 1.16699C6.39858 1.16699 6.08767 1.16699 5.83042 1.30349C5.77355 1.33395 5.71931 1.36907 5.66825 1.40849C5.43783 1.58524 5.30892 1.86816 5.05108 2.43341L4.69758 3.20866M5.54167 9.62533V6.12533M8.45833 9.62533V6.12533"
      stroke="#032282"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default SVGComponent;
