{"name": "liberty-assured", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@studio-freight/lenis": "^1.0.42", "@tanstack/react-query": "^4.36.1", "@tanstack/react-table": "^8.17.3", "axios": "^1.7.2", "axois": "^0.0.1-security", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "framer-motion": "^11.2.9", "lucide-react": "^0.483.0", "moment": "^2.30.1", "next": "14.2.3", "npm": "^10.8.2", "rc-pagination": "^4.2.0", "react": "^18", "react-chartjs-2": "^5.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-pin-input": "^1.3.1", "react-query": "^3.39.3", "react-select": "^5.8.0", "react-use-measure": "^2.1.1", "react-wrap-balancer": "^1.1.1", "recharts": "^2.15.1", "sass": "^1.77.0", "sonner": "^1.4.41", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-check-file": "^2.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-tailwindcss": "^3.17.0", "postcss": "^8", "prettier": "^3.2.5", "tailwindcss": "^3.4.1", "typescript": "^5"}}