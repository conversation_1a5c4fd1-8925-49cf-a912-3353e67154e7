import { DashboardHeader } from "@/components/layout/dashboard";
import { CompanyProvider } from "@/contexts/CompanyContext";
import * as React from "react";

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <CompanyProvider>
      <DashboardHeader />
      <main className="relative bg-main font-wix-display md:px-[4.5rem]">
        {children}
      </main>
    </CompanyProvider>
  );
}
