import * as React from "react";
import { SVGProps } from "react";
const SVGComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={15}
    viewBox="0 0 16 15"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      opacity={0.4}
      d="M10.2324 8.21875H8.87617C7.76367 8.21875 6.85742 7.31875 6.85742 6.2V4.84375C6.85742 4.5875 6.65117 4.375 6.38867 4.375H4.40742C2.96367 4.375 1.79492 5.3125 1.79492 6.9875V11.1375C1.79492 12.8125 2.96367 13.75 4.40742 13.75H8.08867C9.53242 13.75 10.7012 12.8125 10.7012 11.1375V8.6875C10.7012 8.425 10.4887 8.21875 10.2324 8.21875Z"
      fill="#032282"
    />
    <path
      opacity={0.2}
      d="M11.682 1.25H10.4508H9.76953H8.00078C6.58828 1.25 5.44453 2.15 5.39453 3.75625C5.43203 3.75625 5.46328 3.75 5.50078 3.75H7.26953H7.95078H9.18203C10.6258 3.75 11.7945 4.6875 11.7945 6.3625V7.59375V9.2875V10.5188C11.7945 10.5562 11.7883 10.5875 11.7883 10.6187C13.182 10.575 14.2945 9.65 14.2945 8.01875V6.7875V5.09375V3.8625C14.2945 2.1875 13.1258 1.25 11.682 1.25Z"
      fill="#032282"
    />
    <path
      d="M8.03281 4.46835C7.83906 4.2746 7.50781 4.40585 7.50781 4.6746V6.3121C7.50781 6.9996 8.08906 7.5621 8.80156 7.5621C9.24531 7.56835 9.86406 7.56835 10.3953 7.56835C10.6641 7.56835 10.8016 7.25585 10.6141 7.06835C9.93281 6.3871 8.72031 5.16835 8.03281 4.46835Z"
      fill="#032282"
    />
  </svg>
);
export default SVGComponent;
