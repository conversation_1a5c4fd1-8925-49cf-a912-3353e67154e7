'use client';

interface PlayAndWinProps {
  isSelected?: boolean;
}

export function PlayAndWin({ isSelected }: PlayAndWinProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.992 10.842c-2.7 0-4.9-2.2-4.9-4.9s2.2-4.9 4.9-4.9 4.9 2.2 4.9 4.9-2.2 4.9-4.9 4.9Zm0-8.55a3.649 3.649 0 1 0 0 7.3 3.649 3.649 0 1 0 0-7.3ZM5.3 16.825a3.197 3.197 0 0 1-3.191-3.192A3.197 3.197 0 0 1 5.3 10.441a3.197 3.197 0 0 1 3.192 3.192A3.197 3.197 0 0 1 5.3 16.825Zm0-5.134a1.944 1.944 0 0 0-1.941 1.942c0 1.067.866 1.942 1.941 1.942a1.944 1.944 0 0 0 1.942-1.942A1.949 1.949 0 0 0 5.3 11.691Zm8.55 7.267a2.762 2.762 0 0 1-2.758-2.758 2.762 2.762 0 0 1 2.758-2.759 2.767 2.767 0 0 1 2.758 2.759 2.767 2.767 0 0 1-2.758 2.758Zm0-4.275a1.508 1.508 0 1 0 0 3.016 1.508 1.508 0 0 0 0-3.016Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
