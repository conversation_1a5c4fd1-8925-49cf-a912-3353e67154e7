'use client';

interface TransactionHistoryProps {
  isSelected?: boolean;
}

export function TransactionHistory({ isSelected }: TransactionHistoryProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.183 9.792h-2.85a.63.63 0 0 1-.625-.625V3.342c0-.617.242-1.192.675-1.625a2.282 2.282 0 0 1 1.625-.675h.009A3.968 3.968 0 0 1 17.792 2.2 3.97 3.97 0 0 1 18.95 5v2.017c.008 1.658-1.108 2.775-2.767 2.775Zm-2.225-1.25h2.225c.967 0 1.525-.558 1.525-1.525V5c0-.716-.283-1.4-.791-1.916a2.76 2.76 0 0 0-1.9-.792h-.009c-.275 0-.541.108-.741.308-.2.2-.309.459-.309.742v5.2Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M7.5 19.442c-.392 0-.758-.15-1.033-.433l-1.384-1.392a.205.205 0 0 0-.275-.017l-1.433 1.067a1.444 1.444 0 0 1-1.525.142c-.5-.25-.808-.75-.808-1.309V5c0-2.516 1.441-3.958 3.958-3.958h10a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625c-.575 0-1.042.467-1.042 1.042V17.5c0 .559-.308 1.059-.808 1.309s-1.083.2-1.525-.134L10.2 17.61a.203.203 0 0 0-.267.016l-1.4 1.4a1.477 1.477 0 0 1-1.033.417Zm-2.575-3.133c.383 0 .758.141 1.042.433l1.383 1.392c.05.05.117.058.15.058.033 0 .1-.008.15-.058l1.4-1.4a1.45 1.45 0 0 1 1.908-.125l1.417 1.058a.19.19 0 0 0 .217.017c.041-.025.116-.075.116-.184V3.334c0-.375.092-.734.25-1.042H5c-1.85 0-2.708.858-2.708 2.708v12.5c0 .117.075.167.116.192.05.025.134.042.217-.025L4.05 16.6c.258-.191.567-.291.875-.291Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 11.467H7.5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625H10a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm0-3.333H7.5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625H10a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm-5.025.207a.836.836 0 0 1-.833-.833c0-.458.375-.833.833-.833.459 0 .834.375.834.833a.836.836 0 0 1-.834.833Zm0 3.334a.836.836 0 0 1-.833-.833c0-.458.375-.833.833-.833.459 0 .834.375.834.833a.836.836 0 0 1-.834.833Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
