'use client';

interface SavingsProps {
  isSelected?: boolean;
}

export function Savings({ isSelected }: SavingsProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 18.958c-.2 0-.4-.025-.592-.083-4.325-1.192-7.458-5.233-7.458-9.617V5.6c0-.933.675-1.942 1.542-2.3l4.641-1.9a4.909 4.909 0 0 1 3.734 0l4.641 1.9c.867.358 1.542 1.367 1.542 2.3v3.658c0 4.375-3.142 8.417-7.459 9.617a2.028 2.028 0 0 1-.591.083Zm0-16.666c-.475 0-.942.091-1.392.275l-4.641 1.9c-.4.166-.767.708-.767 1.141v3.659c0 3.825 2.75 7.358 6.542 8.408.166.05.35.05.516 0 3.792-1.05 6.542-4.583 6.542-8.408V5.608c0-.433-.367-.975-.767-1.141l-4.641-1.9A3.666 3.666 0 0 0 10 2.292Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 11.041A2.29 2.29 0 0 1 7.708 8.75 2.29 2.29 0 0 1 10 6.458a2.29 2.29 0 0 1 2.292 2.292A2.29 2.29 0 0 1 10 11.04Zm0-3.333a1.042 1.042 0 1 0 0 2.084 1.042 1.042 0 0 0 0-2.084Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 13.542a.63.63 0 0 1-.625-.625v-2.5A.63.63 0 0 1 10 9.792a.63.63 0 0 1 .625.625v2.5a.63.63 0 0 1-.625.625Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
