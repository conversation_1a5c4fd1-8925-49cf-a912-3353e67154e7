import { cva, VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/utils/classNames';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-center text-xs font-medium ring-offset-white transition duration-300 hover:opacity-75 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 active:scale-90 disabled:pointer-events-none disabled:scale-100 disabled:opacity-40 disabled:hover:cursor-not-allowed',
  {
    variants: {
      variant: {
        default: 'bg-main text-white',
        light: 'bg-main-light text-main',
        white: 'bg-white text-main',
        red: 'bg-red-800 text-red-100',
        outlined: 'border border-main bg-transparent text-main',
        outline: "border border-gray-600 bg-transparent text-white hover:bg-gray-800/20 hover:text-white",
        ghost: "bg-transparent text-white hover:bg-gray-800/20 hover:text-white",
        destructive: "bg-red-600 text-white hover:bg-red-700 active:bg-red-800",


        unstyled: '',
      },
      size: {
        default: 'px-6 py-2',
        lg: 'rounded-lg px-6 py-3',
        fullWidth: 'block w-full py-2',
        sm: "h-8 px-3 py-1 text-xs",
        icon: "h-10 w-10 p-0",


        unstyled: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> { }

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
