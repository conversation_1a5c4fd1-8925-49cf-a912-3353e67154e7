import Link from "next/link";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "../core";
import { DrawerClose } from "../core/Drawer";
import { cn } from "@/utils/classNames";

export const LandingHeader = () => {
  return (
    <>
      <header className="bg-[#080D27]">
        <section className="">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 lg:gap-10 xl:gap-[5.625rem]">
              <Link href="/">
                <span className="sr-only">Go home</span>

                <svg
                  fill="none"
                  height={40}
                  viewBox="0 0 155 40"
                  width={155}
                  xmlns="http://www.w3.org/2000/svg"
                  xmlnsXlink="http://www.w3.org/1999/xlink"
                >
                  <rect
                    fill="url(#pattern0_1854_1155)"
                    height={34.3558}
                    width={32}
                    y={3}
                  />
                  <path
                    d="M54.88 20H45.42V5.7H48.12V17.66H54.88V20ZM59.3234 20H56.6834V9.94H59.3234V20ZM58.0034 8.2C57.5634 8.2 57.1834 8.05333 56.8634 7.76C56.5568 7.45333 56.4034 7.08667 56.4034 6.66C56.4034 6.20667 56.5568 5.83333 56.8634 5.54C57.1834 5.24667 57.5634 5.1 58.0034 5.1C58.4568 5.1 58.8368 5.25333 59.1434 5.56C59.4501 5.85333 59.6034 6.22 59.6034 6.66C59.6034 7.08667 59.4501 7.45333 59.1434 7.76C58.8368 8.05333 58.4568 8.2 58.0034 8.2ZM64.2839 20H61.6639V5.6H64.3039V13.06H63.6839C63.7772 12.4067 63.9972 11.82 64.3439 11.3C64.7039 10.78 65.1706 10.3733 65.7439 10.08C66.3306 9.77333 66.9972 9.62 67.7439 9.62C68.6639 9.62 69.5039 9.84667 70.2639 10.3C71.0239 10.7533 71.6239 11.38 72.0639 12.18C72.5172 12.98 72.7439 13.9133 72.7439 14.98C72.7439 16.0333 72.5172 16.96 72.0639 17.76C71.6106 18.56 71.0039 19.1867 70.2439 19.64C69.4839 20.0933 68.6306 20.32 67.6839 20.32C66.8172 20.32 66.0706 20.1333 65.4439 19.76C64.8172 19.3867 64.3439 18.8867 64.0239 18.26C63.7039 17.62 63.5639 16.92 63.6039 16.16L64.1639 15.8C64.2306 16.2533 64.3972 16.66 64.6639 17.02C64.9306 17.3667 65.2706 17.64 65.6839 17.84C66.1106 18.0267 66.5706 18.12 67.0639 18.12C67.6506 18.12 68.1639 17.9867 68.6039 17.72C69.0439 17.4533 69.3839 17.08 69.6239 16.6C69.8772 16.12 70.0039 15.58 70.0039 14.98C70.0039 14.3533 69.8839 13.8067 69.6439 13.34C69.4172 12.86 69.0972 12.4867 68.6839 12.22C68.2706 11.9533 67.7706 11.82 67.1839 11.82C66.6239 11.82 66.1239 11.9533 65.6839 12.22C65.2572 12.4867 64.9172 12.86 64.6639 13.34C64.4239 13.8067 64.3039 14.3733 64.3039 15.04V17.48L64.2839 17.62V20ZM83.3713 16.86L84.7713 18.48C84.2646 19.0533 83.6113 19.5067 82.8113 19.84C82.0246 20.16 81.1513 20.32 80.1913 20.32C79.0313 20.32 78.0113 20.0933 77.1313 19.64C76.2513 19.1867 75.5646 18.56 75.0713 17.76C74.5779 16.96 74.3313 16.0333 74.3313 14.98C74.3313 13.9267 74.5646 13 75.0313 12.2C75.5113 11.3867 76.1579 10.7533 76.9713 10.3C77.7979 9.84667 78.7246 9.62 79.7513 9.62C80.7513 9.62 81.6379 9.84667 82.4113 10.3C83.1979 10.74 83.8113 11.3533 84.2513 12.14C84.6913 12.9267 84.9113 13.84 84.9113 14.88V15H82.3313V14.72C82.3313 14.1067 82.2179 13.5733 81.9913 13.12C81.7779 12.6667 81.4779 12.3133 81.0913 12.06C80.7179 11.8067 80.2713 11.68 79.7513 11.68C79.2046 11.68 78.7246 11.8133 78.3113 12.08C77.8979 12.3333 77.5713 12.7067 77.3313 13.2C77.1046 13.68 76.9913 14.2533 76.9913 14.92C76.9913 15.6267 77.1313 16.2333 77.4113 16.74C77.7046 17.2333 78.0979 17.6133 78.5913 17.88C79.0979 18.1333 79.6779 18.26 80.3313 18.26C81.5313 18.26 82.5446 17.7933 83.3713 16.86ZM84.9113 15.78H76.0312V14.1H84.5913L84.9113 14.88V15.78ZM93.1583 9.76V12.16H92.6183C91.9916 12.16 91.4516 12.2867 90.9983 12.54C90.5449 12.78 90.1849 13.16 89.9183 13.68C89.6649 14.2 89.5383 14.8733 89.5383 15.7V20H86.8983V9.94H89.5183V13.2H89.1983C89.3183 12.3067 89.5649 11.6133 89.9383 11.12C90.3249 10.6267 90.7716 10.28 91.2783 10.08C91.7983 9.86667 92.3049 9.76 92.7983 9.76H93.1583ZM101.387 20H98.867C97.9604 20 97.2737 19.7733 96.807 19.32C96.3537 18.8533 96.127 18.1667 96.127 17.26V7.28H98.767V16.8C98.767 17.16 98.847 17.4267 99.007 17.6C99.167 17.76 99.4204 17.84 99.767 17.84H101.387V20ZM101.267 12.06H94.367V9.94H101.267V12.06ZM110.691 9.94H113.351L108.571 21.16C108.184 22.0533 107.698 22.7267 107.111 23.18C106.524 23.6467 105.744 23.88 104.771 23.88H103.471V21.72H104.631C105.031 21.72 105.364 21.6267 105.631 21.44C105.911 21.2667 106.131 20.9867 106.291 20.6L106.511 20.02L102.211 9.94H104.951L108.231 18.52H107.371L110.691 9.94ZM128.474 20H119.014V5.7H121.714V17.66H128.474V20ZM132.917 20H130.277V9.94H132.917V20ZM131.597 8.2C131.157 8.2 130.777 8.05333 130.457 7.76C130.151 7.45333 129.997 7.08667 129.997 6.66C129.997 6.20667 130.151 5.83333 130.457 5.54C130.777 5.24667 131.157 5.1 131.597 5.1C132.051 5.1 132.431 5.25333 132.737 5.56C133.044 5.85333 133.197 6.22 133.197 6.66C133.197 7.08667 133.044 7.45333 132.737 7.76C132.431 8.05333 132.051 8.2 131.597 8.2ZM139.178 20H136.538V8.42C136.538 6.54 137.478 5.6 139.358 5.6H141.678V7.66H140.278C139.878 7.66 139.591 7.74667 139.418 7.92C139.258 8.08 139.178 8.36 139.178 8.76V20ZM141.678 12.06H134.778V9.94H141.678V12.06ZM151.77 16.86L153.17 18.48C152.663 19.0533 152.01 19.5067 151.21 19.84C150.423 20.16 149.55 20.32 148.59 20.32C147.43 20.32 146.41 20.0933 145.53 19.64C144.65 19.1867 143.963 18.56 143.47 17.76C142.976 16.96 142.73 16.0333 142.73 14.98C142.73 13.9267 142.963 13 143.43 12.2C143.91 11.3867 144.556 10.7533 145.37 10.3C146.196 9.84667 147.123 9.62 148.15 9.62C149.15 9.62 150.036 9.84667 150.81 10.3C151.596 10.74 152.21 11.3533 152.65 12.14C153.09 12.9267 153.31 13.84 153.31 14.88V15H150.73V14.72C150.73 14.1067 150.616 13.5733 150.39 13.12C150.176 12.6667 149.876 12.3133 149.49 12.06C149.116 11.8067 148.67 11.68 148.15 11.68C147.603 11.68 147.123 11.8133 146.71 12.08C146.296 12.3333 145.97 12.7067 145.73 13.2C145.503 13.68 145.39 14.2533 145.39 14.92C145.39 15.6267 145.53 16.2333 145.81 16.74C146.103 17.2333 146.496 17.6133 146.99 17.88C147.496 18.1333 148.076 18.26 148.73 18.26C149.93 18.26 150.943 17.7933 151.77 16.86ZM153.31 15.78H144.43V14.1H152.99L153.31 14.88V15.78Z"
                    fill="white"
                  />
                  <path
                    d="M45.716 37H44.84V28.36H45.728V32.884H45.5C45.572 32.492 45.728 32.148 45.968 31.852C46.216 31.556 46.52 31.324 46.88 31.156C47.248 30.988 47.644 30.904 48.068 30.904C48.644 30.904 49.156 31.044 49.604 31.324C50.052 31.596 50.4 31.968 50.648 32.44C50.904 32.912 51.032 33.448 51.032 34.048C51.032 34.656 50.9 35.196 50.636 35.668C50.38 36.14 50.02 36.512 49.556 36.784C49.1 37.056 48.576 37.192 47.984 37.192C47.448 37.192 46.976 37.084 46.568 36.868C46.168 36.644 45.864 36.344 45.656 35.968C45.456 35.584 45.384 35.156 45.44 34.684L45.608 34.444C45.64 34.82 45.76 35.16 45.968 35.464C46.176 35.76 46.448 35.992 46.784 36.16C47.12 36.328 47.488 36.412 47.888 36.412C48.328 36.412 48.712 36.312 49.04 36.112C49.368 35.904 49.624 35.624 49.808 35.272C50 34.912 50.096 34.504 50.096 34.048C50.096 33.592 50.004 33.188 49.82 32.836C49.644 32.476 49.396 32.196 49.076 31.996C48.756 31.788 48.384 31.684 47.96 31.684C47.528 31.684 47.144 31.792 46.808 32.008C46.472 32.224 46.208 32.524 46.016 32.908C45.824 33.284 45.728 33.724 45.728 34.228V35.38L45.716 35.392V37ZM56.6514 31.096H57.5634L54.5634 38.068C54.3954 38.46 54.1874 38.768 53.9394 38.992C53.6914 39.216 53.3154 39.328 52.8114 39.328H52.2234V38.56H52.8114C53.0674 38.56 53.2754 38.496 53.4354 38.368C53.6034 38.24 53.7394 38.052 53.8434 37.804L54.1554 37.024L51.5994 31.096H52.5594L54.7914 36.532H54.4074L56.6514 31.096ZM66.4844 37H61.1684V28.42H62.0924V36.172H66.4844V37ZM68.6499 37H67.7619V31.096H68.6499V37ZM68.2059 29.512C68.0299 29.512 67.8779 29.452 67.7499 29.332C67.6219 29.204 67.5579 29.052 67.5579 28.876C67.5579 28.692 67.6219 28.54 67.7499 28.42C67.8779 28.292 68.0299 28.228 68.2059 28.228C68.3899 28.228 68.5419 28.292 68.6619 28.42C68.7899 28.54 68.8539 28.692 68.8539 28.876C68.8539 29.052 68.7899 29.204 68.6619 29.332C68.5419 29.452 68.3899 29.512 68.2059 29.512ZM71.2043 37H70.3283V28.36H71.2163V32.884H70.9883C71.0603 32.492 71.2163 32.148 71.4563 31.852C71.7043 31.556 72.0083 31.324 72.3683 31.156C72.7363 30.988 73.1323 30.904 73.5563 30.904C74.1323 30.904 74.6443 31.044 75.0923 31.324C75.5403 31.596 75.8883 31.968 76.1363 32.44C76.3923 32.912 76.5203 33.448 76.5203 34.048C76.5203 34.656 76.3883 35.196 76.1243 35.668C75.8683 36.14 75.5083 36.512 75.0443 36.784C74.5883 37.056 74.0643 37.192 73.4723 37.192C72.9363 37.192 72.4643 37.084 72.0563 36.868C71.6563 36.644 71.3523 36.344 71.1443 35.968C70.9443 35.584 70.8723 35.156 70.9283 34.684L71.0963 34.444C71.1283 34.82 71.2483 35.16 71.4563 35.464C71.6643 35.76 71.9363 35.992 72.2723 36.16C72.6083 36.328 72.9763 36.412 73.3763 36.412C73.8163 36.412 74.2003 36.312 74.5283 36.112C74.8563 35.904 75.1123 35.624 75.2963 35.272C75.4883 34.912 75.5843 34.504 75.5843 34.048C75.5843 33.592 75.4923 33.188 75.3083 32.836C75.1323 32.476 74.8843 32.196 74.5643 31.996C74.2443 31.788 73.8723 31.684 73.4483 31.684C73.0163 31.684 72.6323 31.792 72.2963 32.008C71.9603 32.224 71.6963 32.524 71.5043 32.908C71.3123 33.284 71.2163 33.724 71.2163 34.228V35.38L71.2043 35.392V37ZM82.914 35.32L83.538 35.908C83.226 36.308 82.85 36.624 82.41 36.856C81.97 37.08 81.466 37.192 80.898 37.192C80.274 37.192 79.714 37.056 79.218 36.784C78.73 36.512 78.346 36.14 78.066 35.668C77.786 35.196 77.646 34.656 77.646 34.048C77.646 33.44 77.778 32.9 78.042 32.428C78.314 31.956 78.686 31.584 79.158 31.312C79.63 31.04 80.166 30.904 80.766 30.904C81.342 30.904 81.85 31.04 82.29 31.312C82.738 31.576 83.09 31.94 83.346 32.404C83.602 32.868 83.73 33.404 83.73 34.012V34.084H82.818V34.012C82.818 33.548 82.726 33.144 82.542 32.8C82.366 32.448 82.122 32.176 81.81 31.984C81.506 31.784 81.158 31.684 80.766 31.684C80.334 31.684 79.954 31.784 79.626 31.984C79.298 32.184 79.042 32.46 78.858 32.812C78.674 33.164 78.582 33.572 78.582 34.036C78.582 34.5 78.682 34.912 78.882 35.272C79.082 35.632 79.354 35.912 79.698 36.112C80.05 36.312 80.454 36.412 80.91 36.412C81.742 36.412 82.41 36.048 82.914 35.32ZM83.73 34.36H78.39V33.628H83.538L83.73 34.012V34.36ZM88.2971 31V31.864H88.0571C87.7051 31.864 87.3731 31.956 87.0611 32.14C86.7491 32.324 86.4971 32.612 86.3051 33.004C86.1131 33.396 86.0171 33.904 86.0171 34.528V37H85.1291V31.096H86.0051V32.968H85.8491C85.9451 32.488 86.1131 32.104 86.3531 31.816C86.5931 31.528 86.8691 31.32 87.1811 31.192C87.5011 31.064 87.8291 31 88.1651 31H88.2971ZM92.9442 37H91.5522C91.1362 37 90.8122 36.888 90.5802 36.664C90.3562 36.432 90.2442 36.108 90.2442 35.692V29.368H91.1322V35.62C91.1322 35.812 91.1842 35.964 91.2882 36.076C91.3922 36.18 91.5442 36.232 91.7442 36.232H92.9442V37ZM92.8602 31.852H89.0202V31.096H92.8602V31.852ZM98.6514 31.096H99.5634L96.5634 38.068C96.3954 38.46 96.1874 38.768 95.9394 38.992C95.6914 39.216 95.3154 39.328 94.8114 39.328H94.2234V38.56H94.8114C95.0674 38.56 95.2754 38.496 95.4354 38.368C95.6034 38.24 95.7394 38.052 95.8434 37.804L96.1554 37.024L93.5994 31.096H94.5594L96.7914 36.532H96.4074L98.6514 31.096ZM106.465 34.6H101.797V33.784H106.465V34.6ZM108.097 37H107.125L103.945 28.972H104.353L101.173 37H100.237L103.669 28.42H104.665L108.097 37ZM108.859 36.088L109.483 35.488C109.763 35.784 110.079 36.016 110.431 36.184C110.783 36.352 111.179 36.436 111.619 36.436C112.099 36.436 112.491 36.336 112.795 36.136C113.107 35.928 113.263 35.664 113.263 35.344C113.263 35.064 113.131 34.848 112.867 34.696C112.611 34.536 112.175 34.412 111.559 34.324C110.663 34.196 110.031 34.004 109.663 33.748C109.303 33.484 109.123 33.132 109.123 32.692C109.123 32.332 109.227 32.02 109.435 31.756C109.643 31.484 109.931 31.276 110.299 31.132C110.667 30.98 111.087 30.904 111.559 30.904C112.087 30.904 112.559 31 112.975 31.192C113.391 31.384 113.759 31.656 114.079 32.008L113.455 32.608C113.215 32.312 112.939 32.08 112.627 31.912C112.315 31.744 111.959 31.66 111.559 31.66C111.119 31.66 110.755 31.752 110.467 31.936C110.179 32.12 110.035 32.364 110.035 32.668C110.035 32.892 110.155 33.072 110.395 33.208C110.643 33.336 111.059 33.44 111.643 33.52C112.571 33.648 113.223 33.856 113.599 34.144C113.983 34.424 114.175 34.816 114.175 35.32C114.175 35.688 114.063 36.016 113.839 36.304C113.623 36.584 113.323 36.804 112.939 36.964C112.555 37.116 112.115 37.192 111.619 37.192C111.043 37.192 110.527 37.096 110.071 36.904C109.623 36.704 109.219 36.432 108.859 36.088ZM115.024 36.088L115.648 35.488C115.928 35.784 116.244 36.016 116.596 36.184C116.948 36.352 117.344 36.436 117.784 36.436C118.264 36.436 118.656 36.336 118.96 36.136C119.272 35.928 119.428 35.664 119.428 35.344C119.428 35.064 119.296 34.848 119.032 34.696C118.776 34.536 118.34 34.412 117.724 34.324C116.828 34.196 116.196 34.004 115.828 33.748C115.468 33.484 115.288 33.132 115.288 32.692C115.288 32.332 115.392 32.02 115.6 31.756C115.808 31.484 116.096 31.276 116.464 31.132C116.832 30.98 117.252 30.904 117.724 30.904C118.252 30.904 118.724 31 119.14 31.192C119.556 31.384 119.924 31.656 120.244 32.008L119.62 32.608C119.38 32.312 119.104 32.08 118.792 31.912C118.48 31.744 118.124 31.66 117.724 31.66C117.284 31.66 116.92 31.752 116.632 31.936C116.344 32.12 116.2 32.364 116.2 32.668C116.2 32.892 116.32 33.072 116.56 33.208C116.808 33.336 117.224 33.44 117.808 33.52C118.736 33.648 119.388 33.856 119.764 34.144C120.148 34.424 120.34 34.816 120.34 35.32C120.34 35.688 120.228 36.016 120.004 36.304C119.788 36.584 119.488 36.804 119.104 36.964C118.72 37.116 118.28 37.192 117.784 37.192C117.208 37.192 116.692 37.096 116.236 36.904C115.788 36.704 115.384 36.432 115.024 36.088ZM126.996 37H126.12V35.848L126.108 35.512V31.096H126.996V37ZM126.228 34.444L126.408 34.696C126.448 35.176 126.364 35.604 126.156 35.98C125.948 36.356 125.652 36.652 125.268 36.868C124.892 37.084 124.456 37.192 123.96 37.192C123.248 37.192 122.676 36.98 122.244 36.556C121.82 36.132 121.608 35.52 121.608 34.72V31.096H122.496V34.624C122.496 35.208 122.636 35.652 122.916 35.956C123.204 36.26 123.604 36.412 124.116 36.412C124.476 36.412 124.808 36.332 125.112 36.172C125.424 36.004 125.68 35.772 125.88 35.476C126.08 35.18 126.196 34.836 126.228 34.444ZM131.844 31V31.864H131.604C131.252 31.864 130.92 31.956 130.608 32.14C130.296 32.324 130.044 32.612 129.852 33.004C129.66 33.396 129.564 33.904 129.564 34.528V37H128.676V31.096H129.552V32.968H129.396C129.492 32.488 129.66 32.104 129.9 31.816C130.14 31.528 130.416 31.32 130.728 31.192C131.048 31.064 131.376 31 131.712 31H131.844ZM137.805 35.32L138.429 35.908C138.117 36.308 137.741 36.624 137.301 36.856C136.861 37.08 136.357 37.192 135.789 37.192C135.165 37.192 134.605 37.056 134.109 36.784C133.621 36.512 133.237 36.14 132.957 35.668C132.677 35.196 132.537 34.656 132.537 34.048C132.537 33.44 132.669 32.9 132.933 32.428C133.205 31.956 133.577 31.584 134.049 31.312C134.521 31.04 135.057 30.904 135.657 30.904C136.233 30.904 136.741 31.04 137.181 31.312C137.629 31.576 137.981 31.94 138.237 32.404C138.493 32.868 138.621 33.404 138.621 34.012V34.084H137.709V34.012C137.709 33.548 137.617 33.144 137.433 32.8C137.257 32.448 137.013 32.176 136.701 31.984C136.397 31.784 136.049 31.684 135.657 31.684C135.225 31.684 134.845 31.784 134.517 31.984C134.189 32.184 133.933 32.46 133.749 32.812C133.565 33.164 133.473 33.572 133.473 34.036C133.473 34.5 133.573 34.912 133.773 35.272C133.973 35.632 134.245 35.912 134.589 36.112C134.941 36.312 135.345 36.412 135.801 36.412C136.633 36.412 137.301 36.048 137.805 35.32ZM138.621 34.36H133.281V33.628H138.429L138.621 34.012V34.36ZM145.936 37H145.06V35.392L145.048 35.38V34.228C145.048 33.724 144.956 33.284 144.772 32.908C144.588 32.524 144.328 32.224 143.992 32.008C143.664 31.792 143.284 31.684 142.852 31.684C142.428 31.684 142.052 31.788 141.724 31.996C141.396 32.196 141.14 32.476 140.956 32.836C140.772 33.188 140.68 33.592 140.68 34.048C140.68 34.504 140.772 34.912 140.956 35.272C141.148 35.624 141.408 35.904 141.736 36.112C142.072 36.312 142.456 36.412 142.888 36.412C143.296 36.412 143.664 36.328 143.992 36.16C144.328 35.992 144.6 35.76 144.808 35.464C145.016 35.16 145.136 34.82 145.168 34.444L145.336 34.684C145.4 35.156 145.328 35.584 145.12 35.968C144.912 36.344 144.604 36.644 144.196 36.868C143.796 37.084 143.328 37.192 142.792 37.192C142.2 37.192 141.672 37.056 141.208 36.784C140.752 36.512 140.392 36.14 140.128 35.668C139.872 35.196 139.744 34.656 139.744 34.048C139.744 33.448 139.872 32.912 140.128 32.44C140.392 31.968 140.748 31.596 141.196 31.324C141.652 31.044 142.168 30.904 142.744 30.904C143.176 30.904 143.568 30.988 143.92 31.156C144.28 31.316 144.58 31.544 144.82 31.84C145.06 32.136 145.212 32.484 145.276 32.884H145.048V28.36H145.936V37Z"
                    fill="white"
                  />
                  <defs>
                    <pattern
                      height={1}
                      id="pattern0_1854_1155"
                      patternContentUnits="objectBoundingBox"
                      width={1}
                    >
                      <use
                        transform="scale(0.00306748 0.00285714)"
                        xlinkHref="#image0_1854_1155"
                      />
                    </pattern>
                    <image
                      height={350}
                      id="image0_1854_1155"
                      width={326}
                      xlinkHref="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAUYAAAFeCAYAAAD5UrYGAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACHDwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKL2lDQ1BJQ0MgUHJvZmlsZQAASMedlndUVNcWh8+9d3qhzTDSGXqTLjCA9C4gHQRRGGYGGMoAwwxNbIioQEQREQFFkKCAAaOhSKyIYiEoqGAPSBBQYjCKqKhkRtZKfHl57+Xl98e939pn73P32XuftS4AJE8fLi8FlgIgmSfgB3o401eFR9Cx/QAGeIABpgAwWempvkHuwUAkLzcXerrICfyL3gwBSPy+ZejpT6eD/0/SrFS+AADIX8TmbE46S8T5Ik7KFKSK7TMipsYkihlGiZkvSlDEcmKOW+Sln30W2VHM7GQeW8TinFPZyWwx94h4e4aQI2LER8QFGVxOpohvi1gzSZjMFfFbcWwyh5kOAIoktgs4rHgRm4iYxA8OdBHxcgBwpLgvOOYLFnCyBOJDuaSkZvO5cfECui5Lj25qbc2ge3IykzgCgaE/k5XI5LPpLinJqUxeNgCLZ/4sGXFt6aIiW5paW1oamhmZflGo/7r4NyXu7SK9CvjcM4jW94ftr/xS6gBgzIpqs+sPW8x+ADq2AiB3/w+b5iEAJEV9a7/xxXlo4nmJFwhSbYyNMzMzjbgclpG4oL/rfzr8DX3xPSPxdr+Xh+7KiWUKkwR0cd1YKUkpQj49PZXJ4tAN/zzE/zjwr/NYGsiJ5fA5PFFEqGjKuLw4Ubt5bK6Am8Kjc3n/qYn/MOxPWpxrkSj1nwA1yghI3aAC5Oc+gKIQARJ5UNz13/vmgw8F4psXpjqxOPefBf37rnCJ+JHOjfsc5xIYTGcJ+RmLa+JrCdCAACQBFcgDFaABdIEhMANWwBY4AjewAviBYBAO1gIWiAfJgA8yQS7YDApAEdgF9oJKUAPqQSNoASdABzgNLoDL4Dq4Ce6AB2AEjIPnYAa8AfMQBGEhMkSB5CFVSAsygMwgBmQPuUE+UCAUDkVDcRAPEkK50BaoCCqFKqFaqBH6FjoFXYCuQgPQPWgUmoJ+hd7DCEyCqbAyrA0bwwzYCfaGg+E1cBycBufA+fBOuAKug4/B7fAF+Dp8Bx6Bn8OzCECICA1RQwwRBuKC+CERSCzCRzYghUg5Uoe0IF1IL3ILGUGmkXcoDIqCoqMMUbYoT1QIioVKQ21AFaMqUUdR7age1C3UKGoG9QlNRiuhDdA2aC/0KnQcOhNdgC5HN6Db0JfQd9Dj6DcYDIaG0cFYYTwx4ZgEzDpMMeYAphVzHjOAGcPMYrFYeawB1g7rh2ViBdgC7H7sMew57CB2HPsWR8Sp4sxw7rgIHA+XhyvHNeHO4gZxE7h5vBReC2+D98Oz8dn4Enw9vgt/Az+OnydIE3QIdoRgQgJhM6GC0EK4RHhIeEUkEtWJ1sQAIpe4iVhBPE68QhwlviPJkPRJLqRIkpC0k3SEdJ50j/SKTCZrkx3JEWQBeSe5kXyR/Jj8VoIiYSThJcGW2ChRJdEuMSjxQhIvqSXpJLlWMkeyXPKk5A3JaSm8lLaUixRTaoNUldQpqWGpWWmKtKm0n3SydLF0k/RV6UkZrIy2jJsMWyZf5rDMRZkxCkLRoLhQWJQtlHrKJco4FUPVoXpRE6hF1G+o/dQZWRnZZbKhslmyVbJnZEdoCE2b5kVLopXQTtCGaO+XKC9xWsJZsmNJy5LBJXNyinKOchy5QrlWuTty7+Xp8m7yifK75TvkHymgFPQVAhQyFQ4qXFKYVqQq2iqyFAsVTyjeV4KV9JUCldYpHVbqU5pVVlH2UE5V3q98UXlahabiqJKgUqZyVmVKlaJqr8pVLVM9p/qMLkt3oifRK+g99Bk1JTVPNaFarVq/2ry6jnqIep56q/ojDYIGQyNWo0yjW2NGU1XTVzNXs1nzvhZei6EVr7VPq1drTltHO0x7m3aH9qSOnI6XTo5Os85DXbKug26abp3ubT2MHkMvUe+A3k19WN9CP16/Sv+GAWxgacA1OGAwsBS91Hopb2nd0mFDkqGTYYZhs+GoEc3IxyjPqMPohbGmcYTxbuNe408mFiZJJvUmD0xlTFeY5pl2mf5qpm/GMqsyu21ONnc332jeaf5ymcEyzrKDy+5aUCx8LbZZdFt8tLSy5Fu2WE5ZaVpFW1VbDTOoDH9GMeOKNdra2Xqj9WnrdzaWNgKbEza/2BraJto22U4u11nOWV6/fMxO3Y5pV2s3Yk+3j7Y/ZD/ioObAdKhzeOKo4ch2bHCccNJzSnA65vTC2cSZ79zmPOdi47Le5bwr4urhWuja7ybjFuJW6fbYXd09zr3ZfcbDwmOdx3lPtKe3527PYS9lL5ZXo9fMCqsV61f0eJO8g7wrvZ/46Pvwfbp8Yd8Vvnt8H67UWslb2eEH/Lz89vg98tfxT/P/PgAT4B9QFfA00DQwN7A3iBIUFdQU9CbYObgk+EGIbogwpDtUMjQytDF0Lsw1rDRsZJXxqvWrrocrhHPDOyOwEaERDRGzq91W7109HmkRWRA5tEZnTdaaq2sV1iatPRMlGcWMOhmNjg6Lbor+wPRj1jFnY7xiqmNmWC6sfaznbEd2GXuKY8cp5UzE2sWWxk7G2cXtiZuKd4gvj5/munAruS8TPBNqEuYS/RKPJC4khSW1JuOSo5NP8WR4ibyeFJWUrJSBVIPUgtSRNJu0vWkzfG9+QzqUvia9U0AV/Uz1CXWFW4WjGfYZVRlvM0MzT2ZJZ/Gy+rL1s3dkT+S453y9DrWOta47Vy13c+7oeqf1tRugDTEbujdqbMzfOL7JY9PRzYTNiZt/yDPJK817vSVsS1e+cv6m/LGtHlubCyQK+AXD22y31WxHbedu799hvmP/jk+F7MJrRSZF5UUfilnF174y/ariq4WdsTv7SyxLDu7C7OLtGtrtsPtoqXRpTunYHt897WX0ssKy13uj9l4tX1Zes4+wT7hvpMKnonO/5v5d+z9UxlfeqXKuaq1Wqt5RPXeAfWDwoOPBlhrlmqKa94e4h+7WetS212nXlR/GHM44/LQ+tL73a8bXjQ0KDUUNH4/wjowcDTza02jV2Nik1FTSDDcLm6eORR67+Y3rN50thi21rbTWouPguPD4s2+jvx064X2i+yTjZMt3Wt9Vt1HaCtuh9uz2mY74jpHO8M6BUytOdXfZdrV9b/T9kdNqp6vOyJ4pOUs4m3924VzOudnzqeenL8RdGOuO6n5wcdXF2z0BPf2XvC9duex++WKvU++5K3ZXTl+1uXrqGuNax3XL6+19Fn1tP1j80NZv2d9+w+pG503rm10DywfODjoMXrjleuvyba/b1++svDMwFDJ0dzhyeOQu++7kvaR7L+9n3J9/sOkh+mHhI6lH5Y+VHtf9qPdj64jlyJlR19G+J0FPHoyxxp7/lP7Th/H8p+Sn5ROqE42TZpOnp9ynbj5b/Wz8eerz+emCn6V/rn6h++K7Xxx/6ZtZNTP+kv9y4dfiV/Kvjrxe9rp71n/28ZvkN/NzhW/l3x59x3jX+z7s/cR85gfsh4qPeh+7Pnl/eriQvLDwG/eE8/s3BCkeAAAACXBIWXMAAC4jAAAuIwF4pT92AAAAIXRFWHRDcmVhdGlvbiBUaW1lADIwMjQ6MDU6MDIgMTU6MzQ6MTYO3FPmAAAG2klEQVR4Xu3cv4tdaR3A4T2ZuzsjzizoVoJpIlr4B7hNhBWENAr+aBbFSgtTKrFZ/wKJtsHKVoXtXBC0NZVbiI0IETfWIcJMoUm49/gOc6oPgoGb5M5cnwfevOc7xWHOOzkf5jYzrdfr70/T9OPXXoBxr6+vVqsPlxFeOz09/cTJycmflxFelo+P9ebF5Xbmef7BeRjvjDDeXb62lXGvmyOM95cRXjs7O3vr+Pj40TLCpTfC+N615RqAhTAChDAChDAChDAChDAChDAChDAChDAChDAChDAChDAChDAChL+uw0t1enr6yZOTk78uI7wsHxvr/E+PbW2e5x8JI3DlbTab26Nj95ZxK+Net3yUBghhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBAhhBIhpvV7fmabp7jJvZdzr5mq1ur+Me2We51+O7WsX026M8/3iON8Pl3HvjTP/7djeuZiuvIPxnr2+XO/EOM+/jO/h88u4Vzabze3xbPeWcSvjXrf8xvj8zv9TH+14TWP9P3ljrP92DldxvT7CdB6nna1hdf4P/5swAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAoQwAsS0Xq/vTNN0d5m3Mu51c7Va3V/GvTLP8/tj++bFtBvjfL8wzvePy7j3njx58plr166dLOOVdnBw8PZ4z36+jLvy6/E9vLtc75XNZnN7PNu9ZdzKuNctYXxOwsg2xs/uyyPyv1/GXfnFeNe/u1zvlRcdRh+lAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYAUIYn9/by74zBwcHf5jn+V97sn61PBZcOsL4/KZl36U3xjrak3X+LHApCSNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNACCNATOv1+s40TXeXeSvjXjdXq9X9Zdwr49m+NM7p08u4E5vN5u9j+/fFdLWN8/zn4eHh35Zx7z1+/PjN4XPLuBPjzB+NM/9oGffKeDduj/fz3jJuZdzrljACV96LDqOP0gAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAhjAAxbTabn439hxfj1n4yz/OD5ZpL4unTp787Ojr6xzK+Ug8ePDi8cePGd5YRXoppmt4Z27cvpu2Mhn3rPIy/GddfufgS+2j8jL96cHDwwTK+UmdnZ28dHx8/Wka49EYY3/NRGiCEESCEESCEESCEESCEESCEESCEESCEESCEESCEESCEESCEESCEESCm9Xr97jRN31jmrczz/KexnV5MXBbPnj374PDw8KNlfKUePnx4dP369e8tI7wUo2GfGttnL6btrNfrn/4HsmgxhKtyW3cAAAAASUVORK5CYII="
                    />
                  </defs>
                </svg>
              </Link>
            </div>
            <ul className="hidden md:flex items-center justify-center space-x-5 text-xs">
              <li>
                <a href="/">Home</a>
              </li>
              <li>
                <a href="/">Plan</a>
              </li>
              <li>
                <a href="/learn-more">About us</a>
              </li>
              <li>
                <a href="/faqs">FAQs</a>
              </li>
              <li>
                <a href="/contact-us">Contact us</a>
              </li>
            </ul>
            <ul className="hidden md:flex justify-end items-center space-x-5">
              <li>
                <Link href="/">login</Link>
              </li>
              <button className="flex justify-between items-center bg-white text-blue-950 bg rounded-full text-xs py-1 pl-5 pr-2 gap-[18px]">
                Get insurance
                <svg
                  width="30"
                  height="30"
                  viewBox="0 0 30 30"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="15" cy="15" r="15" fill="#032282" />
                  <path
                    d="M10.9168 19.6171C11.0334 19.6171 11.1501 19.5587 11.2084 19.5004L19.3751 11.3337C19.5501 11.1587 19.5501 10.9254 19.3751 10.7504C19.2001 10.5754 18.9084 10.5754 18.7334 10.7504L10.5668 18.9171C10.3918 19.0921 10.3918 19.3837 10.5668 19.5587C10.6834 19.6171 10.8001 19.6171 10.9168 19.6171Z"
                    fill="white"
                  />
                  <path
                    d="M19.0834 17.4585C19.3167 17.4585 19.55 17.2835 19.55 16.9919V11.0419C19.55 10.8085 19.375 10.5752 19.0834 10.5752H13.075C12.8417 10.5752 12.6084 10.7502 12.6084 11.0419C12.6084 11.3335 12.7834 11.5085 13.075 11.5085H18.6167V17.0502C18.6167 17.2835 18.85 17.4585 19.0834 17.4585Z"
                    fill="white"
                  />
                </svg>
              </button>
            </ul>
            <DrawerMenu
              trigger={
                <Button
                  className={cn(
                    "md:hidden bg-white/10 px-5 py-2.5 rounded-full",
                    "font-display"
                  )}
                >
                  Menu
                </Button>
              }
              contentClass="bg-main border-main"
            >
              <div className="text-white p-5 pb-0 gap-5">
                <header className="flex items-center justify-between">
                  <h6 className="font-semibold text-lg">Menu Content</h6>
                  <DrawerClose
                    className={cn(
                      "bg-white/10 h-8 w-8 rounded-full text-white/50 rotate-12 text-lg hover:text-white",
                      "font-display"
                    )}
                  >
                    x
                  </DrawerClose>
                </header>

                <ul
                  className={cn(
                    "font-display",
                    "flex flex-col gap-8 font-normal mt-10"
                  )}
                >
                  <li className="border-b-[.1504px] border-b-white/30 p-2">
                    <Link href="/">Home</Link>
                  </li>
                  <li className="border-b-[.1504px] border-b-white/30 p-2">
                    <Link href="/">Products</Link>
                  </li>
                  <li className="border-b-[.1504px] border-b-white/30 p-2">
                    <Link href="/">Company</Link>
                  </li>
                  <li className="border-b-[.1504px] border-b-white/30 p-2">
                    <Link href="/">About us</Link>
                  </li>
                  <li className="border-b-[.1504px] border-b-white/30 p-2">
                    <Link href="/">Contact us</Link>
                  </li>
                </ul>
              </div>
            </DrawerMenu>
          </div>
        </section>
      </header>
    </>
  );
};
