'use client';

interface ProfileAndSettingsProps {
  isSelected?: boolean;
}

export function ProfileAndSettings({ isSelected }: ProfileAndSettingsProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 18.966c-.908 0-1.808-.266-2.517-.791L3.9 15.5c-.95-.709-1.692-2.184-1.692-3.367V5.941c0-1.283.942-2.65 2.15-3.1l4.159-1.558c.825-.308 2.125-.308 2.95 0l4.166 1.558c1.209.45 2.15 1.817 2.15 3.1v6.192c0 1.183-.741 2.658-1.691 3.367l-3.584 2.675c-.7.525-1.6.791-2.508.791ZM8.958 2.45 4.8 4.008c-.717.267-1.342 1.167-1.342 1.933v6.192c0 .792.559 1.9 1.184 2.367l3.583 2.675c.958.716 2.583.716 3.542 0L15.35 14.5c.633-.475 1.183-1.584 1.183-2.367V5.941c0-.758-.625-1.658-1.341-1.933L11.033 2.45c-.55-.209-1.516-.209-2.075 0Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M10 9.725h-.058C8.733 9.69 7.85 8.766 7.85 7.64c0-1.15.942-2.091 2.092-2.091 1.15 0 2.091.941 2.091 2.091a2.095 2.095 0 0 1-2.016 2.092c-.009-.008-.009-.008-.017-.008ZM9.942 6.8a.84.84 0 0 0-.842.841c0 .459.358.825.808.842H10a.834.834 0 0 0 .783-.842.834.834 0 0 0-.841-.841ZM10 14.459c-.717 0-1.442-.192-2-.567-.558-.367-.875-.909-.875-1.483 0-.575.317-1.117.875-1.492 1.125-.75 2.883-.742 4 0 .558.367.875.908.875 1.483 0 .575-.317 1.117-.875 1.492-.559.375-1.284.567-2 .567ZM8.69 11.95c-.208.134-.325.3-.316.45 0 .15.117.317.316.45.7.467 1.917.467 2.617 0 .209-.133.325-.3.325-.45 0-.15-.116-.316-.316-.45-.7-.458-1.925-.458-2.626 0Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
