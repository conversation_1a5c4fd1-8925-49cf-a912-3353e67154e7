"use client";
import React, { useEffect, useState, FormEvent } from "react";

import { z } from "zod";
import { Label } from "@radix-ui/react-label";
import { Input2 } from "@/components/core/Input2";
import { <PERSON>, SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, ErrorModal, LinkButton } from "@/components/core";



import { formatAxiosErrorMessage } from "@/utils";
import { AxiosError } from "axios";
import { useErrorModalState } from "@/hooks";
import { SmallSpinner } from "@/icons/core";

import { useRouter } from "next/navigation";
import { OnboardingPageWrapper } from "./misc";
import { Checkbox } from "@/components/core/Checkbox";

import { useUser } from "@/app/(auth)/(onboarding)/misc";
import { set } from "date-fns";

import { EyeOff } from "lucide-react";
import VisibleSVG from "./misc/components/icon/VisibleSVG";
import NoVisibleSVG from "./misc/components/icon/NotVisibleSVG";
import { useAuthUserDetails } from "./api/authUserDetails";

const contactSchema = z.object({
  email: z.string().email("Invalid email address"),
  
  password: z
    .string()
    .min(6, { message: "Password must be at least 8 characters long." }),
    // .max(8, { message: "Password must be at most 8 characters long." }), 
    
});
export type userStatusType = z.infer<typeof contactSchema>;

export default function Login() {
  const router = useRouter();

  const { mutate: submitForm, isLoading } = useAuthUserDetails();

  const {
    isErrorModalOpen,
    setErrorModalState,
    // closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
  } = useErrorModalState();
  const {
    register,
    handleSubmit,

    formState: { errors },
  } = useForm<userStatusType>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      email: "",
      password: "",
    },

    mode: "onChange",
  });

  

  const onSubmit = (data:userStatusType) => {
    console.log(data);
    const updateData ={
      ...data,
      device_type: "MOBILE"
    }
    submitForm(updateData,
      {
        onSuccess: (data) => {
          console.log("success", data);
          router.push("/dashboard");
        },
       onError(error, variables, context) {
          console.log("error", error);
          if (error instanceof AxiosError) {
            const errorMessage = formatAxiosErrorMessage(error);
            openErrorModalWithMessage(errorMessage as string);
          } else {
            openErrorModalWithMessage(
              "An unexpected error occurred. Please try again."
            );
          }
       },
      }
    )
  };
  const [visible, setVisible] = useState(false)

  return (
    <>
      <OnboardingPageWrapper
        heading="Welcome Admin! 👋"
        subHeading="Enter your Email and Password to Login "
      >
        <form className="mt-8" onSubmit={handleSubmit(onSubmit)}>
          <div className="w-full mt-[1rem] text-sm font-normal">
            <div className={`relative  mb-3 mt-[8px]`}>
              <div className="">
                <label className=" text-[14px] text-white">Email:</label>
                <Input2
                  className={`${errors?.email ? "border border-red-700" : "border border-[#898989]"} h-12 rounded-lg outline-none text-[#fff] bg-transparent  border-opacity-50 py-1.5 px-[20px] !autofill:bg-red`}
                  placeholder="Enter your email"
                  type="text"
                  id="email"
                  disabled={isLoading}
                  {...register("email")}
                />
                {errors?.email && (
                  <p className="text-red-600">{errors?.email?.message}</p>
                )}
              </div>
              <div className="mb-2 mt-4 relative">
                <label className="mb-[7px] mt-[8px]  text-[14px] text-white">
                  Password:
                </label>

                <Input2
                  className={`${errors?.password ? "border border-red-700" : "border border-[#898989]"}  h-12 rounded-lg text-[#fff] bg-transparent  border-opacity-50 py-1.5 px-[20px]`}
                  placeholder="Enter your password"
                  type={visible ? "text" : "password"}
                  id="password"
                  disabled={isLoading}
                 {...register("password")}/>

                 <div className="p-2 absolute top-5 left-80 text-white opacity-80">
                  {/* {visible ? <EyeVisible onClick={()=>setVisible(!visible)} className="absolute top-4 right-4 cursor-pointer"/> : <EyeInvisible onClick={()=>setVisible(!visible)} className="absolute top-4 right-4 cursor-pointer"/>} */}
                  <button type="button" onClick={()=>setVisible(!visible)} className="absolute top-4 right-4 cursor-pointer">
                    {visible ? <VisibleSVG /> :<NoVisibleSVG  />}
                  </button>
                 </div>
                
                
                {errors?.password && (
                  <p className="text-red-600">{errors?.password?.message}</p>
                )}
              </div>

              <div className="flex items-center space-x-2 mb-4 mt-4">
                <Checkbox id="terms" />
                <label
                  htmlFor="terms"
                  className="text-xs font-normal text-white leading-none "
                >
                  Keep me logged in.
                </label>
              </div>
{/* <button onClick={()=>setVisible(!visible)} className="text-white">show</button> */}
              <div className="">
                <Button
                  className=" mt-[5rem] flex items-center justify-center gap-x-2 font-display focus:shadow-outline w-full rounded-lg bg-[#fff] p-4 py-3 font-semibold tracking-wide
                                    shadow-lg transition-colors delay-150 ease-in-out hover:bg-slate-300 focus:outline-none text-[#1B1687]"
                  type="submit"
                  disabled={isLoading}
                >
                  {/* {isLoading && (
                    <SmallSpinner className="h-4 w-4 animate-spin text-white" />
                  )} */}
                  {isLoading ? "Loading..." : "Login"} {/*Could replace string with a spinner */}
                </Button>
              </div>
            </div>
          </div>
        </form>

        {errorModalMessage !== "User not found" && (
          <ErrorModal
            isErrorModalOpen={isErrorModalOpen}
            setErrorModalState={setErrorModalState}
            subheading={
              errorModalMessage || "Please check your inputs and try again."
            }
          />
        )}
      </OnboardingPageWrapper>
    </>
  );
}
