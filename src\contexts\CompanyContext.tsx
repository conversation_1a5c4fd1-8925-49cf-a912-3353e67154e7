"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface CompanyContextType {
  selectedCompany: string | null;
  setSelectedCompany: (company: string | null) => void;
  isCompanySelected: boolean;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

interface CompanyProviderProps {
  children: ReactNode;
}

export const CompanyProvider: React.FC<CompanyProviderProps> = ({ children }) => {
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null);

  const value: CompanyContextType = {
    selectedCompany,
    setSelectedCompany,
    isCompanySelected: selectedCompany !== null,
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
};

export const useCompany = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};
