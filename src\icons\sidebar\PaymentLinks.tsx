'use client';

interface PaymentLinksProps {
  isSelected?: boolean;
}

export function PaymentLinks({ isSelected }: PaymentLinksProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.417 12.292H8.334a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h2.083a3.962 3.962 0 0 0 3.958-3.959 3.962 3.962 0 0 0-3.958-3.958H6.25a3.962 3.962 0 0 0-3.958 3.958c0 .917.325 1.809.908 2.517a.632.632 0 0 1-.083.883.632.632 0 0 1-.883-.083 5.197 5.197 0 0 1-1.2-3.317 5.21 5.21 0 0 1 5.208-5.208h4.167a5.21 5.21 0 0 1 5.208 5.208 5.203 5.203 0 0 1-5.2 5.209Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
      <path
        d="M13.75 18.125H9.583a5.21 5.21 0 0 1-5.208-5.209 5.21 5.21 0 0 1 5.208-5.208h2.084a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625H9.583a3.962 3.962 0 0 0-3.958 3.958 3.962 3.962 0 0 0 3.958 3.959h4.167a3.962 3.962 0 0 0 3.958-3.959c0-.916-.325-1.808-.908-2.516a.632.632 0 0 1 .083-.884.624.624 0 0 1 .884.084 5.197 5.197 0 0 1 1.2 3.316 5.223 5.223 0 0 1-5.217 5.209Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
