import { adminAxios } from "@/lib/axios";

import { useQuery } from '@tanstack/react-query';

// import { UserDataTypes } from "../types";

export interface NoPinError {
  error: string;
  message: string;
  create_transaction_pin_link: string;
}

export interface UserDataTypes {
  status: string
  status_code: number
  data: Data
  errors: any
}

export interface Data {
  companies: Company[]
  overall: Overall
}

export interface Company {
  company_name: string
  balance: number
  cash_flow: CashFlow
}

export interface CashFlow {
  total_inflow: number
  total_outflow: number
}

export interface Overall {
  cash_flow: CashFlow2
  balance: number
}

export interface CashFlow2 {
  total_inflow: number
  total_outflow: number
}



export const getAccountDetails = async (companyName?: string) => {
  const url = companyName
    ? `/dashboard/account_transaction_details/?company=${encodeURIComponent(companyName)}`
    : `/dashboard/account_transaction_details/`;
  const response = await adminAxios.get(url);
  return response.data as UserDataTypes;
};


export const useAccountDetails = (companyName?: string) => {
  return useQuery({
    queryKey: ["accounts-details", companyName],
    queryFn: () => getAccountDetails(companyName),
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchOnMount: true
  })

}
// export const useUser = () =>
//   useQuery("user-details", getAuthenticatedUser, { retry: 2 });