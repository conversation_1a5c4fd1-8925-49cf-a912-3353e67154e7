'use client';

interface BankPerformanceProps {
  isSelected?: boolean;
}

export function BankPerformance({ isSelected }: BankPerformanceProps) {
  return (
    <svg
      fill="none"
      height={20}
      viewBox="0 0 20 20"
      width={20}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 12.291A2.29 2.29 0 0 1 7.708 10 2.29 2.29 0 0 1 10 7.708 2.29 2.29 0 0 1 12.292 10 2.29 2.29 0 0 1 10 12.29Zm0-3.333a1.042 1.042 0 1 0 0 2.084 1.042 1.042 0 0 0 0-2.084Zm6.667 6.667a.625.625 0 0 1-.5-1A7.641 7.641 0 0 0 17.709 10a7.641 7.641 0 0 0-1.542-4.625.625.625 0 0 1 1-.75A8.854 8.854 0 0 1 18.959 10a8.853 8.853 0 0 1-1.792 5.375.616.616 0 0 1-.5.25Zm-13.334 0a.616.616 0 0 1-.5-.25A8.853 8.853 0 0 1 1.042 10c0-1.958.616-3.816 1.791-5.375a.625.625 0 0 1 1 .75A7.641 7.641 0 0 0 2.292 10a7.64 7.64 0 0 0 1.541 4.625.625.625 0 0 1-.5 1Zm10.667-2a.625.625 0 0 1-.5-1A4.306 4.306 0 0 0 14.375 10c0-.958-.3-1.866-.875-2.625a.625.625 0 0 1 1-.75A5.609 5.609 0 0 1 15.625 10a5.59 5.59 0 0 1-1.125 3.375.616.616 0 0 1-.5.25Zm-8 0a.616.616 0 0 1-.5-.25A5.608 5.608 0 0 1 4.375 10c0-1.225.392-2.4 1.125-3.375a.625.625 0 0 1 1 .75A4.306 4.306 0 0 0 5.625 10c0 .959.3 1.867.875 2.625a.625.625 0 0 1-.5 1Z"
        fill={isSelected ? '#5879FD' : '#fff'}
      />
    </svg>
  );
}
