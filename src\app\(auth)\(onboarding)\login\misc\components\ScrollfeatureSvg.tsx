import * as React from "react";
const ScrollAnimationSVG = () => (
    <svg
        fill="none"
        height={109}
        viewBox="0 0 145 109"
        width={145}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
    >
        <path
            d="M5.232 84.681H0.72V68.601H5.232V84.681ZM14.52 84.681H1.968V80.625H14.52V84.681ZM20.232 84.681H15.72V72.585H20.232V84.681ZM20.232 71.481H15.72V68.601H20.232V71.481ZM30.7931 84.921C29.3851 84.921 28.3051 84.641 27.5531 84.081C26.8011 83.505 26.3291 82.617 26.1371 81.417H25.3931L25.7291 78.657H26.1851C26.1851 79.233 26.2891 79.681 26.4971 80.001C26.7211 80.305 27.0491 80.513 27.4811 80.625C27.9291 80.737 28.4891 80.793 29.1611 80.793C29.8651 80.793 30.4171 80.729 30.8171 80.601C31.2331 80.457 31.5291 80.225 31.7051 79.905C31.8811 79.585 31.9691 79.161 31.9691 78.633C31.9691 78.089 31.8811 77.665 31.7051 77.361C31.5291 77.041 31.2331 76.809 30.8171 76.665C30.4171 76.521 29.8651 76.449 29.1611 76.449C28.1531 76.449 27.4011 76.577 26.9051 76.833C26.4251 77.089 26.1851 77.641 26.1851 78.489H25.7291V75.705H26.4251C26.6011 74.697 27.0491 73.889 27.7691 73.281C28.4891 72.657 29.5611 72.345 30.9851 72.345C32.1691 72.345 33.1691 72.601 33.9851 73.113C34.8171 73.609 35.4491 74.329 35.8811 75.273C36.3131 76.201 36.5291 77.321 36.5291 78.633C36.5291 79.929 36.3051 81.049 35.8571 81.993C35.4251 82.937 34.7771 83.665 33.9131 84.177C33.0651 84.673 32.0251 84.921 30.7931 84.921ZM25.8971 84.681H21.6731V68.601H26.1851V80.961L25.8971 81.273V84.681ZM44.4718 84.921C43.1118 84.921 41.9038 84.713 40.8478 84.297C39.7918 83.881 38.9678 83.209 38.3758 82.281C37.7838 81.353 37.4878 80.137 37.4878 78.633C37.4878 77.321 37.7838 76.201 38.3758 75.273C38.9678 74.329 39.7758 73.609 40.7998 73.113C41.8398 72.601 43.0318 72.345 44.3758 72.345C45.7678 72.345 46.9758 72.577 47.9998 73.041C49.0238 73.489 49.8158 74.161 50.3758 75.057C50.9358 75.937 51.2158 77.025 51.2158 78.321C51.2158 78.529 51.2078 78.721 51.1918 78.897C51.1918 79.057 51.1758 79.249 51.1438 79.473H40.4398V77.193H47.8798L46.8958 78.609C46.8958 78.465 46.8958 78.329 46.8958 78.201C46.8958 78.057 46.8958 77.913 46.8958 77.769C46.8958 77.033 46.7038 76.521 46.3198 76.233C45.9518 75.945 45.2718 75.801 44.2798 75.801C43.2558 75.801 42.5598 75.961 42.1918 76.281C41.8398 76.601 41.6638 77.177 41.6638 78.009V79.161C41.6638 80.009 41.8478 80.585 42.2158 80.889C42.5838 81.193 43.2798 81.345 44.3038 81.345C45.2158 81.345 45.8238 81.265 46.1278 81.105C46.4478 80.929 46.6078 80.665 46.6078 80.313V80.049H51.1198V80.337C51.1198 81.233 50.8478 82.025 50.3038 82.713C49.7598 83.401 48.9918 83.945 47.9998 84.345C47.0078 84.729 45.8318 84.921 44.4718 84.921ZM56.9351 84.681H52.4231V72.585H56.6471V76.041L56.9351 76.161V84.681ZM56.9351 77.457H56.0951V75.753H56.8871C56.9991 75.081 57.2151 74.489 57.5351 73.977C57.8551 73.465 58.2871 73.065 58.8311 72.777C59.3911 72.489 60.0711 72.345 60.8711 72.345C61.7671 72.345 62.4871 72.529 63.0311 72.897C63.5751 73.249 63.9671 73.737 64.2071 74.361C64.4631 74.985 64.5911 75.705 64.5911 76.521V78.921H60.0791V77.505C60.0791 76.993 59.9671 76.641 59.7431 76.449C59.5191 76.241 59.1111 76.137 58.5191 76.137C57.9111 76.137 57.4951 76.241 57.2711 76.449C57.0471 76.641 56.9351 76.977 56.9351 77.457ZM74.5961 84.681H71.4281C69.9241 84.681 68.7401 84.305 67.8761 83.553C67.0281 82.801 66.6041 81.641 66.6041 80.073V70.497H71.1161V79.425C71.1161 79.905 71.2041 80.225 71.3801 80.385C71.5721 80.545 71.9241 80.625 72.4361 80.625H74.5961V84.681ZM74.5961 76.281H64.8281V72.585H74.5961V76.281ZM78.3371 88.761H76.1531V84.681H79.6571C80.0251 84.681 80.3051 84.633 80.4971 84.537C80.6891 84.441 80.8331 84.273 80.9291 84.033L81.2651 83.217L80.8331 85.233L74.7131 72.585H79.8491L81.9371 77.337L82.8011 80.121H83.1131L83.9051 77.289L85.7051 72.585H90.7451L85.0091 85.161C84.5771 86.105 84.0651 86.841 83.4731 87.369C82.8971 87.897 82.1931 88.257 81.3611 88.449C80.5291 88.657 79.5211 88.761 78.3371 88.761ZM95.7476 84.681H91.2356V68.601H95.7476V84.681ZM105.036 84.681H92.4836V80.625H105.036V84.681ZM110.748 84.681H106.236V72.585H110.748V84.681ZM110.748 71.481H106.236V68.601H110.748V71.481ZM117.733 84.681H113.221V73.545C113.221 72.633 113.469 71.809 113.965 71.073C114.477 70.321 115.237 69.721 116.245 69.273C117.253 68.825 118.501 68.601 119.989 68.601H120.949V71.817H119.269C118.421 71.817 117.869 71.921 117.613 72.129C117.373 72.321 117.413 72.801 117.733 73.569V74.289V84.681ZM120.949 76.281H111.589V72.585H120.949V76.281ZM128.659 84.921C127.299 84.921 126.091 84.713 125.035 84.297C123.979 83.881 123.155 83.209 122.563 82.281C121.971 81.353 121.675 80.137 121.675 78.633C121.675 77.321 121.971 76.201 122.563 75.273C123.155 74.329 123.963 73.609 124.987 73.113C126.027 72.601 127.219 72.345 128.563 72.345C129.955 72.345 131.163 72.577 132.187 73.041C133.211 73.489 134.003 74.161 134.563 75.057C135.123 75.937 135.403 77.025 135.403 78.321C135.403 78.529 135.395 78.721 135.379 78.897C135.379 79.057 135.363 79.249 135.331 79.473H124.627V77.193H132.067L131.083 78.609C131.083 78.465 131.083 78.329 131.083 78.201C131.083 78.057 131.083 77.913 131.083 77.769C131.083 77.033 130.891 76.521 130.507 76.233C130.139 75.945 129.459 75.801 128.467 75.801C127.443 75.801 126.747 75.961 126.379 76.281C126.027 76.601 125.851 77.177 125.851 78.009V79.161C125.851 80.009 126.035 80.585 126.403 80.889C126.771 81.193 127.467 81.345 128.491 81.345C129.403 81.345 130.011 81.265 130.315 81.105C130.635 80.929 130.795 80.665 130.795 80.313V80.049H135.307V80.337C135.307 81.233 135.035 82.025 134.491 82.713C133.947 83.401 133.179 83.945 132.187 84.345C131.195 84.729 130.019 84.921 128.659 84.921Z"
            fill="white"
        />
        <path
            d="M19.488 101.681H18.72V93.641H19.572V97.409H19.632C19.884 96.389 20.736 95.633 22.164 95.633C24.024 95.633 25.044 96.917 25.044 98.717C25.044 100.517 24.024 101.801 22.092 101.801C20.772 101.801 19.824 101.117 19.548 99.929H19.488V101.681ZM19.572 98.813C19.572 100.217 20.46 101.021 21.888 101.021C23.304 101.021 24.18 100.433 24.18 98.717C24.18 97.001 23.28 96.425 21.912 96.425C20.412 96.425 19.572 97.241 19.572 98.705V98.813ZM26.8711 103.721H26.1271V102.941H27.0271C27.6271 102.941 27.8551 102.761 28.0831 102.269L28.3711 101.669L25.4311 95.753H26.3671L28.1071 99.281L28.7671 100.733H28.8391L29.4751 99.269L31.1191 95.753H32.0551L28.8391 102.509C28.4071 103.421 27.8551 103.721 26.8711 103.721ZM41.0314 101.681H35.0794V93.641H35.9314V100.901H41.0314V101.681ZM42.8454 94.997H41.9934V93.641H42.8454V94.997ZM42.8454 101.681H41.9934V95.753H42.8454V101.681ZM45.0466 101.681H44.2786V93.641H45.1306V97.409H45.1906C45.4426 96.389 46.2946 95.633 47.7226 95.633C49.5826 95.633 50.6026 96.917 50.6026 98.717C50.6026 100.517 49.5826 101.801 47.6506 101.801C46.3306 101.801 45.3826 101.117 45.1066 99.929H45.0466V101.681ZM45.1306 98.813C45.1306 100.217 46.0186 101.021 47.4466 101.021C48.8626 101.021 49.7386 100.433 49.7386 98.717C49.7386 97.001 48.8386 96.425 47.4706 96.425C45.9706 96.425 45.1306 97.241 45.1306 98.705V98.813ZM54.4697 101.801C52.5617 101.801 51.3497 100.601 51.3497 98.717C51.3497 96.917 52.5497 95.633 54.4577 95.633C56.1977 95.633 57.4337 96.641 57.4337 98.405C57.4337 98.621 57.4097 98.801 57.3737 98.957H52.1537C52.2017 100.289 52.8857 101.093 54.4577 101.093C55.8497 101.093 56.4977 100.577 56.4977 99.713V99.629H57.3497V99.713C57.3497 100.949 56.1257 101.801 54.4697 101.801ZM54.4457 96.341C52.9097 96.341 52.2137 97.133 52.1537 98.429H56.6297C56.6297 98.369 56.6297 98.309 56.6297 98.249C56.6297 97.001 55.8377 96.341 54.4457 96.341ZM59.4158 101.681H58.5638V95.753H59.3318V97.373H59.3918C59.5718 96.425 60.2438 95.633 61.4798 95.633C62.8478 95.633 63.4478 96.641 63.4478 97.745V98.333H62.5958V97.877C62.5958 96.869 62.1758 96.377 61.1438 96.377C59.9438 96.377 59.4158 97.133 59.4158 98.465V101.681ZM68.0708 101.681H66.7868C65.6108 101.681 64.8548 101.189 64.8548 99.797V96.485H63.7988V95.753H64.8548V94.337H65.7188V95.753H68.0708V96.485H65.7188V99.845C65.7188 100.673 66.1268 100.901 66.9908 100.901H68.0708V101.681ZM69.7852 103.721H69.0412V102.941H69.9412C70.5412 102.941 70.7692 102.761 70.9972 102.269L71.2852 101.669L68.3452 95.753H69.2812L71.0212 99.281L71.6812 100.733H71.7532L72.3892 99.269L74.0332 95.753H74.9692L71.7532 102.509C71.3212 103.421 70.7692 103.721 69.7852 103.721ZM76.0663 101.681H75.1303L78.7663 93.641H79.9543L83.6023 101.681H82.6423L81.5623 99.317H77.1463L76.0663 101.681ZM78.8743 95.501L77.4943 98.549H81.2143L79.8223 95.501L79.3783 94.457H79.3063L78.8743 95.501ZM86.9842 101.801C85.1602 101.801 84.1042 101.069 84.1042 99.701V99.677H84.9562V99.749C84.9562 100.745 85.5442 101.093 86.9962 101.093C88.3282 101.093 88.7842 100.781 88.7842 100.097C88.7842 99.473 88.4122 99.269 87.4282 99.113L85.8442 98.885C84.7642 98.741 84.0802 98.321 84.0802 97.337C84.0802 96.293 85.0762 95.633 86.7082 95.633C88.4122 95.633 89.5162 96.353 89.5162 97.745V97.769H88.6642V97.709C88.6642 96.821 88.1962 96.341 86.6842 96.341C85.4362 96.341 84.9202 96.641 84.9202 97.349C84.9202 97.961 85.2322 98.177 86.2162 98.321L87.6682 98.525C88.9402 98.705 89.6242 99.137 89.6242 100.097C89.6242 101.213 88.5202 101.801 86.9842 101.801ZM93.3474 101.801C91.5234 101.801 90.4674 101.069 90.4674 99.701V99.677H91.3194V99.749C91.3194 100.745 91.9074 101.093 93.3594 101.093C94.6914 101.093 95.1474 100.781 95.1474 100.097C95.1474 99.473 94.7754 99.269 93.7914 99.113L92.2074 98.885C91.1274 98.741 90.4434 98.321 90.4434 97.337C90.4434 96.293 91.4394 95.633 93.0714 95.633C94.7754 95.633 95.8794 96.353 95.8794 97.745V97.769H95.0274V97.709C95.0274 96.821 94.5594 96.341 93.0474 96.341C91.7994 96.341 91.2834 96.641 91.2834 97.349C91.2834 97.961 91.5954 98.177 92.5794 98.321L94.0314 98.525C95.3034 98.705 95.9874 99.137 95.9874 100.097C95.9874 101.213 94.8834 101.801 93.3474 101.801ZM99.5907 101.801C97.8987 101.801 97.0707 100.625 97.0707 99.221V95.753H97.9227V99.089C97.9227 100.325 98.5227 101.033 99.8907 101.033C101.343 101.033 102.099 100.157 102.099 98.633V95.753H102.951V101.681H102.183V99.833H102.123C101.919 100.841 101.067 101.801 99.5907 101.801ZM105.236 101.681H104.384V95.753H105.152V97.373H105.212C105.392 96.425 106.064 95.633 107.3 95.633C108.668 95.633 109.268 96.641 109.268 97.745V98.333H108.416V97.877C108.416 96.869 107.996 96.377 106.964 96.377C105.764 96.377 105.236 97.133 105.236 98.465V101.681ZM113.075 101.801C111.167 101.801 109.955 100.601 109.955 98.717C109.955 96.917 111.155 95.633 113.063 95.633C114.803 95.633 116.039 96.641 116.039 98.405C116.039 98.621 116.015 98.801 115.979 98.957H110.759C110.807 100.289 111.491 101.093 113.063 101.093C114.455 101.093 115.103 100.577 115.103 99.713V99.629H115.955V99.713C115.955 100.949 114.731 101.801 113.075 101.801ZM113.051 96.341C111.515 96.341 110.819 97.133 110.759 98.429H115.235C115.235 98.369 115.235 98.309 115.235 98.249C115.235 97.001 114.443 96.341 113.051 96.341ZM119.725 101.801C117.829 101.801 116.773 100.517 116.773 98.717C116.773 96.917 117.829 95.633 119.653 95.633C121.057 95.633 121.933 96.389 122.197 97.409H122.257V93.641H123.109V101.681H122.329V99.929H122.281C121.993 101.117 121.033 101.801 119.725 101.801ZM117.637 98.717C117.637 100.433 118.549 101.021 119.941 101.021C121.333 101.021 122.257 100.217 122.257 98.813V98.705C122.257 97.241 121.381 96.425 119.917 96.425C118.561 96.425 117.637 97.001 117.637 98.717Z"
            fill="white"
        />
        <rect fill="url(#pattern0_1461_1835)" height={53.681} width={50} x={47} />
        <defs>
            <pattern
                height={1}
                id="pattern0_1461_1835"
                patternContentUnits="objectBoundingBox"
                width={1}
            >
                <use
                    transform="scale(0.00306748 0.00285714)"
                    xlinkHref="#image0_1461_1835"
                />
            </pattern>
            {/* <image
                height={350}
                id="image0_1461_1835"
                width={326}
                xlinkHref="data:image/png;base64,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"
            /> */}
        </defs>
    </svg>
);
export default ScrollAnimationSVG;
