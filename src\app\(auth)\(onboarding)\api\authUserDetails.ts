

import { useAuth } from '@/contexts/authentication';


import { adminAxios, setAxiosDefaultToken, tokenlessAuthAxios } from '@/lib/axios';


import type { AxiosResponse } from 'axios';
import { getAuthenticatedUser } from '../misc/api';
import { tokenStorage } from '../misc';
import { useMutation } from '@tanstack/react-query';


export interface AuthUserDetails {
    email: string
    password: string
    device_type: string
}

// interface TokenResponse{
//   access:string;
//   refresh:string
// }

export interface TokenResponse {
  status: string
  status_code: number
  data: TokenResponseEntity
  errors: any
}

export interface TokenResponseEntity {
  status: boolean
  message: string
  company_id: string
  access: string
  refresh: string
}


const login = (loginPhoneNumberDto: AuthUserDetails): Promise<AxiosResponse<TokenResponse>> =>
  tokenlessAuthAxios.post('/api/v1/companies/auth/login/', loginPhoneNumberDto);



export const useAuthUserDetails = () => {
  const {authDispatch} = useAuth();

 return useMutation(login,{
  onSuccess: async ({data}) => {
    const {access:token} = data.data;


    tokenStorage.setToken(token);
    setAxiosDefaultToken(token, adminAxios);
     const user = await getAuthenticatedUser();

    if(authDispatch){
      authDispatch({type:"LOGIN", payload:user});
      authDispatch({type:"STOP_LOADING"})
    }
  }
 })
}
